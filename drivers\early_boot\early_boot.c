#include "early_boot.h"
#include "CH58x_common.h"

// 简单的延时函数（不依赖系统时钟）
static void early_delay_ms(uint32_t ms)
{
    volatile uint32_t i, j;
    for(i = 0; i < ms; i++) {
        for(j = 0; j < 1000; j++) {
            __NOP();
        }
    }
}

// 早期串口输出（简化版）
static void early_print(const char* str)
{
    // 使用简单的延时输出，避免依赖复杂的串口初始化
    // 在早期启动阶段，我们主要依靠LED指示
    (void)str;  // 暂时不输出串口信息，避免初始化问题
}

// LED控制函数
void early_led_on(void)
{
    GPIOB_SetBits(EARLY_LED_PIN);
}

void early_led_off(void)
{
    GPIOB_ResetBits(EARLY_LED_PIN);
}

void early_led_power_on_indicate(void)
{
    early_led_on();
    early_delay_ms(500);  // 亮0.5秒
    early_led_off();
}

void early_led_wakeup_indicate(void)
{
    // 闪烁两次
    for(int i = 0; i < 2; i++) {
        early_led_on();
        early_delay_ms(200);
        early_led_off();
        early_delay_ms(200);
    }
}

// 睡眠保持RAM管理
bool early_is_wakeup_from_sleep(void)
{
    early_wakeup_info_t* info = EARLY_WAKEUP_INFO_PTR;
    
    early_print("Early: Checking wakeup info\r\n");
    
    // 检查魔数和唤醒源
    if(info->magic_number == EARLY_WAKEUP_MAGIC && 
       info->wakeup_source == WAKEUP_FROM_SLEEP) {
        early_print("Early: Detected wakeup from sleep\r\n");
        
        // 立即清除唤醒标志，避免下次误判
        early_clear_wakeup_info();
        return true;
    }
    
    early_print("Early: Detected power on boot\r\n");
    return false;
}

void early_set_wakeup_source(uint8_t source)
{
    early_wakeup_info_t* info = EARLY_WAKEUP_INFO_PTR;
    
    info->magic_number = EARLY_WAKEUP_MAGIC;
    info->wakeup_source = source;
    info->boot_count++;
}

void early_clear_wakeup_info(void)
{
    early_wakeup_info_t* info = EARLY_WAKEUP_INFO_PTR;
    
    info->magic_number = 0;
    info->wakeup_source = WAKEUP_FROM_POWER_ON;
    info->boot_count = 0;
}

// 配置GPIO唤醒并进入睡眠
void early_enter_powerdown(void)
{
    // 配置LED引脚为输出（确保LED关闭）
    GPIOB_ModeCfg(EARLY_LED_PIN, GPIO_ModeOut_PP_5mA);
    early_led_off();

    // 配置唤醒引脚：PA0(E键), PB8(Q键), PB9(R键)
    // 设置为上拉输入
    GPIOA_ModeCfg(GPIO_Pin_0, GPIO_ModeIN_PU);
    GPIOB_ModeCfg(GPIO_Pin_8 | GPIO_Pin_9, GPIO_ModeIN_PU);

    // 清除所有GPIO中断标志
    GPIOA_ClearITFlagBit(GPIO_Pin_0);
    GPIOB_ClearITFlagBit(GPIO_Pin_8 | GPIO_Pin_9);

    // 设置睡眠标志
    early_set_wakeup_source(WAKEUP_FROM_SLEEP);

    // 延时确保设置完成
    early_delay_ms(50);

    // 进入深度睡眠，保持RAM
    LowPower_Shutdown(0);

    // 如果执行到这里，说明睡眠失败
    while(1) {
        early_delay_ms(1000);
    }
}

// 早期启动检查函数
void early_boot_check(void)
{
    // 基本的GPIO时钟使能
    PWR_PeriphClkCfg(ENABLE, RB_CLK_GPIO_EN);

    // 配置LED引脚
    GPIOB_ModeCfg(EARLY_LED_PIN, GPIO_ModeOut_PP_5mA);
    early_led_off();

    // 先测试LED功能
    early_led_on();
    early_delay_ms(100);
    early_led_off();
    early_delay_ms(100);

    // 检查是否从睡眠唤醒
    if(early_is_wakeup_from_sleep()) {
        // 从睡眠唤醒 - 闪烁两次
        early_led_wakeup_indicate();
        // 继续正常启动流程
        return;
    } else {
        // 上电启动 - 亮0.5秒然后进入睡眠
        early_led_power_on_indicate();

        // 给用户3秒时间观察
        for(int i = 0; i < 3; i++) {
            early_led_on();
            early_delay_ms(200);
            early_led_off();
            early_delay_ms(800);
        }

        // 立即进入睡眠模式
        early_enter_powerdown();

        // 这里不会执行到
    }
}
