#ifndef __EARLY_BOOT_H
#define __EARLY_BOOT_H

#include <stdint.h>
#include <stdbool.h>
#include "CH58x_common.h"

#ifdef __cplusplus
extern "C" {
#endif

// 睡眠保持RAM区域定义 (使用高地址RAM区域)
#define SLEEP_RETAIN_RAM_BASE   0x20007F00  // 使用最后256字节
#define SLEEP_RETAIN_RAM_SIZE   256

// 唤醒标志结构体
typedef struct {
    uint32_t magic_number;      // 魔数，用于验证数据有效性
    uint8_t wakeup_source;      // 唤醒源标识
    uint8_t boot_count;         // 启动计数
    uint8_t reserved[2];        // 保留字节
} early_wakeup_info_t;

// 魔数定义
#define EARLY_WAKEUP_MAGIC      0x87654321

// 唤醒源定义
#define WAKEUP_FROM_POWER_ON    0x00    // 上电启动
#define WAKEUP_FROM_SLEEP       0x01    // 从睡眠唤醒

// 睡眠保持RAM访问指针
#define EARLY_WAKEUP_INFO_PTR   ((early_wakeup_info_t*)SLEEP_RETAIN_RAM_BASE)

// LED控制引脚定义 (PB12)
#define EARLY_LED_PIN           GPIO_Pin_12

// 函数声明
void early_boot_check(void);
bool early_is_wakeup_from_sleep(void);
void early_set_wakeup_source(uint8_t source);
void early_clear_wakeup_info(void);
void early_led_on(void);
void early_led_off(void);
void early_led_power_on_indicate(void);
void early_led_wakeup_indicate(void);
void early_enter_powerdown(void);

#ifdef __cplusplus
}
#endif

#endif /* __EARLY_BOOT_H */
