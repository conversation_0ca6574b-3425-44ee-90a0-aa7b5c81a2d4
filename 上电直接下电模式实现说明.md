# 上电直接下电模式实现说明

## 功能概述
实现了设备上电后直接进入下电模式的功能，通过PB12 LED进行状态指示，使用睡眠保持RAM区域区分上电和唤醒状态。

## 主要特性

### 1. 启动行为
- **首次上电**: LED亮0.5秒后直接进入下电模式
- **从睡眠唤醒**: LED闪烁两次（每次亮0.5秒，间隔0.3秒）后正常启动

### 2. LED指示功能
- **上电指示**: PB12 LED亮0.5秒
- **唤醒指示**: PB12 LED闪烁两次，每次亮0.5秒，间隔0.3秒
- **下电模式**: LED熄灭，设备进入极低功耗状态

### 3. 睡眠保持RAM
- **保持区域**: 0x20007C00-0x20007FFF (1KB)
- **保持内容**: 唤醒标志、启动计数等信息
- **魔数验证**: 使用0x12345678验证数据有效性

## 实现的文件

### 1. drivers/power_led/power_led.h
**核心定义:**
```c
// LED控制引脚 (PB12)
#define POWER_LED_PIN           GPIO_Pin_12
#define POWER_LED_PORT          GPIOB

// 睡眠保持RAM区域
#define SLEEP_RETAIN_RAM_BASE   0x20007C00
#define SLEEP_RETAIN_RAM_SIZE   1024

// 唤醒信息结构体
typedef struct {
    uint32_t magic_number;      // 魔数验证
    uint8_t wakeup_source;      // 唤醒源标识
    uint8_t boot_count;         // 启动计数
    uint8_t reserved[2];        // 保留字节
} wakeup_info_t;

// 唤醒源定义
#define WAKEUP_SOURCE_POWER_ON  0x00    // 上电启动
#define WAKEUP_SOURCE_SLEEP     0x01    // 从睡眠唤醒
```

### 2. drivers/power_led/power_led.c
**主要功能:**
- LED控制函数 (`power_led_on/off`)
- 上电指示 (`power_led_power_on_indicate`)
- 唤醒指示 (`power_led_wakeup_indicate`)
- 睡眠保持RAM管理 (`is_wakeup_from_sleep`, `set_wakeup_source`)
- 立即下电模式 (`enter_powerdown_immediately`)

### 3. APP/src/main.c
**启动逻辑修改:**
```c
// 初始化电源LED和唤醒信息
power_led_init();
init_wakeup_info();

// 检查是否从睡眠唤醒
if(is_wakeup_from_sleep()) {
    PRINT("Wakeup from sleep mode\n");
    // 唤醒指示：闪烁两次
    power_led_wakeup_indicate();
    
    // 正常初始化系统
    Mode_Init(device_mode);
    tmos_start_task( halTaskID, HAL_ADC_EVENT, MS1_TO_SYSTEM_TIME(6*1000));
    Main_Circulation();
} else {
    PRINT("Power on boot\n");
    // 上电指示：亮0.5秒
    power_led_power_on_indicate();
    
    // 立即进入下电模式，等待按键唤醒
    enter_powerdown_immediately();
}
```

### 4. subsys/PM/pm_task.c
**下电模式增强:**
```c
// 设置唤醒标志，表示下次启动是从睡眠唤醒
set_wakeup_source(WAKEUP_SOURCE_SLEEP);

// 进入深度睡眠模式，保持部分RAM
LowPower_Shutdown(RB_PWR_RAM2K); // 保持2KB RAM，包含唤醒信息
```

### 5. GPIO中断处理增强
**key_task.c 和 soc.c:**
- 在GPIO中断处理函数中添加唤醒标志设置
- 确保唤醒时正确标记唤醒源

## 工作流程

### 1. 首次上电流程
```
系统启动 → 初始化LED和RAM → 检查唤醒信息 → 
判断为上电启动 → LED亮0.5秒 → 进入下电模式
```

### 2. 按键唤醒流程
```
按下E/Q/R键 → GPIO中断触发 → 设置唤醒标志 → 
系统复位重启 → 检查唤醒信息 → 判断为睡眠唤醒 → 
LED闪烁两次 → 正常启动系统
```

### 3. 睡眠保持RAM管理
```
进入睡眠前: 设置魔数和唤醒源 → 保存到RAM保持区域
唤醒后: 检查魔数 → 验证唤醒源 → 决定启动行为
```

## 技术特点

### 1. 睡眠保持RAM
- **保持策略**: 使用`LowPower_Shutdown(RB_PWR_RAM2K)`保持2KB RAM
- **数据验证**: 使用魔数0x12345678验证数据完整性
- **容错处理**: 魔数错误时自动重置为上电状态

### 2. LED指示设计
- **硬件控制**: 直接控制PB12引脚，无需复杂的PWM
- **时序精确**: 使用DelayMs确保准确的指示时间
- **功耗优化**: 指示完成后立即关闭LED

### 3. 唤醒源管理
- **多源支持**: 支持E(PA0)、Q(PB8)、R(PB9)三个按键唤醒
- **状态跟踪**: 记录启动计数，便于调试和统计
- **可扩展性**: 易于添加新的唤醒源

## 功耗优化

### 1. 下电模式功耗
- **RAM保持**: 只保持2KB RAM，其余RAM断电
- **GPIO配置**: 大部分GPIO下拉输入，降低漏电流
- **唤醒GPIO**: 只有3个按键上拉输入并启用中断
- **预期功耗**: ~50-100μA

### 2. LED指示功耗
- **短时指示**: 上电指示仅0.5秒，唤醒指示约1.3秒
- **即时关闭**: 指示完成后立即关闭LED
- **低占空比**: LED开启时间占比极低

## 调试和验证

### 1. 调试输出
```c
PRINT("Power on boot\n");           // 上电启动
PRINT("Wakeup from sleep mode\n");  // 睡眠唤醒
PRINT("Wakeup by E/Q/R key\n");     // 具体唤醒按键
```

### 2. 状态验证
- 检查LED指示是否正确
- 验证睡眠保持RAM数据
- 确认唤醒源识别准确

### 3. 功耗测试
- 测量下电模式实际功耗
- 验证LED指示期间的功耗
- 确认唤醒响应时间

## 使用说明

### 1. 首次使用
- 设备上电后LED亮0.5秒，然后进入下电模式
- 此时设备处于极低功耗状态，等待按键唤醒

### 2. 唤醒操作
- 按下E、Q、R任意一个按键
- 设备立即唤醒，LED闪烁两次表示从睡眠唤醒
- 系统正常启动，进入工作模式

### 3. 工作模式
- 唤醒后设备正常工作
- 1分钟无按键操作后自动进入下电模式
- 下次唤醒仍会有LED闪烁指示

## 优势

1. **极低功耗**: 上电后立即进入下电模式，最大化电池寿命
2. **直观指示**: 通过LED清晰区分上电和唤醒状态
3. **可靠唤醒**: 三个独立按键确保唤醒可靠性
4. **状态保持**: 睡眠保持RAM确保状态信息不丢失
5. **用户友好**: 无需复杂操作，按键即可唤醒使用

这个实现完美满足了你的需求：设备上电后直接进入下电模式，通过LED指示区分上电和唤醒状态，使用睡眠保持RAM确保状态信息的可靠性。
