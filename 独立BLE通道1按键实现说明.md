# 独立BLE通道1按键实现说明

## 功能概述
将原本的fn+r组合键功能（BLE通道1管理）通过(PA2,PB16)这个独立按键来实现。

## 原始fn+r功能分析
- **短按fn+r**: 切换到BLE通道1，清除绑定信息
- **长按fn+r**: 重新生成BLE通道1的MAC地址，用于重新配对
- **工作模式**: 只在BLE模式下生效

## 按键位置分析
- **GPIO组合**: (PA2,PB16)
- **矩阵位置**: 行3(PA2), 列6(PB16)
- **按键索引**: 6 × 7 + 3 = 45

## 修改的文件

### 1. drivers/key_scan/key_table.h
**修改按键映射表:**
```c
0x00, 0x00, 0x23, 0xfe, 0x00, 0x00, 0x00, // c6 - 索引43-48
//                 ^^^^
//              BLE_CH1(45)
```
- 将索引45的HID值设置为0xfe（特殊标识，与原fn键相同）

### 2. drivers/key_scan/key_parse.h
**新增标志声明:**
```c
extern bool ble_ch1_key_flag;
```

### 3. drivers/key_scan/key_parse.c
**新增标志定义和处理逻辑:**
```c
bool ble_ch1_key_flag = false;

// 在hotkeydeal函数中添加:
if (index[i] == 45) // BLE通道1按键 (PA2,PB16)
{
    ble_ch1_key_flag = true;
    index[i] = 0;
    ret = true;
}
```

### 4. drivers/key_scan/key_special.c
**新增独立按键处理逻辑:**

#### 4.1 添加头文件和变量
```c
#include "soc.h"  // 包含get_current_time函数

// 独立BLE通道1按键处理
static void handle_independent_ble_ch1_key(bool is_long);
static bool ble_ch1_key_pressed = false;
static uint32_t ble_ch1_key_press_time = 0;
```

#### 4.2 添加处理函数
```c
static void handle_independent_ble_ch1_key(bool is_long)
{
    struct speical_data data = {0};
    data.key_state = true;
    data.long_key_flag = is_long;
    
    PRINT("Independent BLE CH1 key: %s\n", is_long ? "long press" : "short press");
    
    // 直接调用BLE通道1处理函数
    ble_chan_1(&data);
}
```

#### 4.3 修改special_key_handler函数
```c
// 在函数开头添加独立按键处理逻辑
if (ble_ch1_key_flag) {
    ble_ch1_key_flag = false;  // 清除标志
    
    if (!ble_ch1_key_pressed) {
        // 按键按下
        ble_ch1_key_pressed = true;
        ble_ch1_key_press_time = get_current_time();
    }
} else if (ble_ch1_key_pressed) {
    // 按键释放
    ble_ch1_key_pressed = false;
    uint32_t press_duration = get_current_time() - ble_ch1_key_press_time;
    bool is_long_press = (press_duration > LONGKEY_TIME);
    
    handle_independent_ble_ch1_key(is_long_press);
}
```

## 工作流程

### 1. 按键检测流程
```
按键按下 → keyScan() → 检测到索引45 → hotkeydeal() → 设置ble_ch1_key_flag
```

### 2. 按键处理流程
```
special_key_handler() → 检测ble_ch1_key_flag → 记录按下时间 → 等待释放
```

### 3. 按键释放流程
```
按键释放 → 计算按压时长 → 判断长短按 → 调用handle_independent_ble_ch1_key()
```

### 4. 功能执行流程
```
handle_independent_ble_ch1_key() → 构造speical_data → 调用ble_chan_1()
```

## 技术特点

### 1. 时间管理
- 使用系统提供的`get_current_time()`函数（定义在soc.h中）
- 时间单位为毫秒，基于RTC时钟
- 长按判断阈值使用现有的`LONGKEY_TIME`常量

### 2. 状态管理
- `ble_ch1_key_pressed`: 跟踪按键按下状态
- `ble_ch1_key_press_time`: 记录按键按下时间
- `ble_ch1_key_flag`: 标识按键事件发生

### 3. 功能复用
- 直接调用原有的`ble_chan_1()`函数
- 保持与原fn+r功能完全一致的行为
- 无需重复实现BLE通道管理逻辑

## 功能验证

### 1. 短按功能
- 按下(PA2,PB16)按键并快速释放
- 应该切换到BLE通道1
- 清除当前通道的绑定信息

### 2. 长按功能
- 按下(PA2,PB16)按键并保持超过LONGKEY_TIME
- 应该重新生成BLE通道1的MAC地址
- 准备重新配对

### 3. 调试输出
- 控制台会输出按键状态信息
- 显示是短按还是长按
- 显示BLE通道切换信息

## 注意事项

1. **模式依赖**: 功能只在BLE模式下生效，与原fn+r行为一致
2. **去抖动**: 利用现有的按键去抖动机制
3. **时间精度**: 使用毫秒级时间精度，足够准确判断长短按
4. **资源占用**: 新增的静态变量和函数占用极少的内存和代码空间
5. **兼容性**: 不影响其他按键的正常工作

## 优势

1. **独立操作**: 无需组合键，单键即可实现BLE通道管理
2. **功能完整**: 保持原有fn+r的所有功能特性
3. **实现简洁**: 复用现有代码，减少重复开发
4. **易于使用**: 用户操作更加直观和便捷
