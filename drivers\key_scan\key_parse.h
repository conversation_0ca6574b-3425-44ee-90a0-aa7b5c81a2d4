/*
 * Copyright (c) 2022 zerosensei
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#ifndef DRIVERS_KEY_SCAN_KEY_PARSE_H
#define DRIVERS_KEY_SCAN_KEY_PARSE_H

#include <stdint.h>
#include <stdbool.h>
#include <string.h>

// 独立按键索引定义
#define INDEPENDENT_KEY_E_INDEX     200  // E键索引
#define INDEPENDENT_KEY_Q_INDEX     201  // Q键索引
#define INDEPENDENT_KEY_R_INDEX     202  // R键索引

struct key16_type {
    uint8_t index;
    uint8_t val;
};

int key_parse(uint8_t *key_map, uint8_t num, uint8_t key8[8], uint8_t key16[16]);

extern bool key_fn_flag;
extern bool ble_ch1_key_flag;

#endif /* DRIVERS_KEY_SCAN_KEY_PARSE_H */
