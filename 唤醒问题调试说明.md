# 唤醒问题调试说明

## 问题现象
设备上电后能正常进入下电模式，但无法通过E、Q、R按键唤醒。

## 可能的原因分析

### 1. LowPower_Shutdown参数问题 ✅ 已修复
- **问题**: 使用了未定义的`RB_PWR_RAM2K`常量
- **修复**: 改为使用`LowPower_Shutdown(0)`保持所有RAM

### 2. GPIO中断配置问题
- **可能问题**: GPIO中断标志位没有清除
- **修复**: 添加了`GPIOA_ClearITFlagBit`和`GPIOB_ClearITFlagBit`

### 3. 中断优先级或使能问题
- **可能问题**: GPIO中断没有正确使能或优先级设置有问题
- **需要验证**: 检查PFIC中断控制器配置

### 4. GPIO配置时序问题
- **可能问题**: GPIO配置后立即进入睡眠，配置可能没有生效
- **修复**: 增加了延时时间

## 调试步骤

### 第一步：验证GPIO中断配置
添加了详细的调试输出：
```c
PRINT("Configured wakeup pins: PA0(E), PB8(Q), PB9(R)\n");
PRINT("GPIO interrupt flags cleared, entering shutdown...\n");
```

### 第二步：验证睡眠保持RAM
添加了详细的RAM检查输出：
```c
PRINT("Checking wakeup info at 0x%08X\n", (uint32_t)info);
PRINT("Magic number: 0x%08X (expected: 0x%08X)\n", info->magic_number, WAKEUP_MAGIC_NUMBER);
PRINT("Wakeup source: %d (expected: %d for sleep)\n", info->wakeup_source, WAKEUP_SOURCE_SLEEP);
```

### 第三步：简化测试流程
暂时简化了启动逻辑，专注于测试基本的下电和唤醒功能。

## 修复的问题

### 1. LowPower_Shutdown参数
```c
// 修复前
LowPower_Shutdown(RB_PWR_RAM2K);  // 未定义的常量

// 修复后  
LowPower_Shutdown(0);  // 保持所有RAM
```

### 2. GPIO中断标志清除
```c
// 新增
GPIOA_ClearITFlagBit(GPIO_Pin_0);  // 清除E键中断标志
GPIOB_ClearITFlagBit(GPIO_Pin_8 | GPIO_Pin_9);  // 清除Q、R键中断标志
```

### 3. 增加调试延时
```c
// 修改前
DelayMs(2);

// 修改后
DelayMs(10);  // 增加延时确保GPIO配置生效
```

## 下一步调试建议

### 1. 检查硬件连接
- 确认E键连接到PA0
- 确认Q键连接到PB8  
- 确认R键连接到PB9
- 确认按键有正确的上拉电阻

### 2. 测试GPIO中断功能
可以在正常工作模式下测试GPIO中断是否正常：
```c
// 在main函数中添加测试代码
GPIOA_ModeCfg(GPIO_Pin_0, GPIO_ModeIN_PU);
GPIOA_ITModeCfg(GPIO_Pin_0, GPIO_ITMode_FallEdge);
PFIC_EnableIRQ(GPIO_A_IRQn);
```

### 3. 检查中断处理函数
确认GPIO中断处理函数是否被正确调用：
```c
__HIGH_CODE
__INTERRUPT
void GPIOA_IRQHandler(void)
{
    PRINT("GPIOA interrupt triggered!\n");  // 添加调试输出
    if(GPIOA_ReadITFlagBit(GPIO_Pin_0)) {
        GPIOA_ClearITFlagBit(GPIO_Pin_0);
        PRINT("E key interrupt\n");
    }
}
```

### 4. 验证LowPower_Shutdown行为
可能需要检查：
- LowPower_Shutdown是否真的进入了睡眠模式
- 睡眠模式下GPIO中断是否能正常工作
- 是否需要特殊的唤醒配置

### 5. 替代方案测试
如果GPIO中断唤醒有问题，可以尝试：
- 使用定时器唤醒测试基本的LowPower_Shutdown功能
- 参考原有的`pm_goto_standby`函数的GPIO配置方式

## 当前测试版本的特点

1. **保持所有RAM**: 使用`LowPower_Shutdown(0)`确保数据不丢失
2. **详细调试输出**: 添加了完整的状态检查和调试信息
3. **清除中断标志**: 确保GPIO中断状态干净
4. **增加延时**: 给GPIO配置足够的时间生效

## 预期的调试输出

正常情况下应该看到：
```
Power on boot
Power on LED indicate
Enter powerdown immediately
Set wakeup source: 1, boot count: 2
Configured wakeup pins: PA0(E), PB8(Q), PB9(R)
GPIO interrupt flags cleared, entering shutdown...
```

按键唤醒后应该看到：
```
Checking wakeup info at 0x20007C00
Magic number: 0x12345678 (expected: 0x12345678)
Wakeup source: 1 (expected: 1 for sleep)
Boot count: 2
Detected wakeup from sleep
Wakeup from sleep mode
Wakeup LED indicate
```

如果看不到唤醒后的输出，说明GPIO中断唤醒确实有问题，需要进一步调试硬件配置或中断处理。
