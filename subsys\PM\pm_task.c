/*
 * Copyright (c) 2022 zerosensei
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#include "pm_task.h"
#include "device_config.h"
#include "key_scan/keyscan.h"
#include "USB_task/USB_kbd.h"
#include "HAL/HAL.h"
#include "BLE/hidkbd.h"
#include "CH58xBLE_LIB.h"
#include "power_led/power_led.h"

uint8_t pm_task_id = 0;
uint8_t ideflag=0;
#define CONFIG_PM_DBG

#ifdef CONFIG_PM_DBG
#define PM_DBG  PRINT
#else
#define PM_DBG(...)
#endif

#if (defined (HAL_SLEEP)) && (HAL_SLEEP == TRUE)
u8 pm_is_idle = false;

static void pm_tmos_msg(tmos_event_hdr_t *pMsg)
{
    switch (pMsg->event) {

    default:
        break;
    }
}

u8 pm_is_in_idle(void)
{
    return pm_is_idle;
}

void pm_start_working(int working_timeout, int idle_timeout)
{
//    PRINT("Enter pm working\n");
//    PM_DBG("Enter pm working\n");
    pm_is_idle = false;

    if(working_timeout >= 0) {
//        PRINT("working_timeout >= 0\n");
        tmos_start_task(pm_task_id, PM_ENTER_IDLE_EVENT, MS1_TO_SYSTEM_TIME(working_timeout));

        if(idle_timeout >=0) {
//            PRINT("dle_timeout >=0\n");
            tmos_start_task(pm_task_id, PM_ENTER_STANDBY_EVENT, MS1_TO_SYSTEM_TIME(idle_timeout));
        } else {
            //PM_DBG("Warning: invalid idle_time! \n");
        }
    }
}

void pm_goto_standby(void)
{
//    PM_DBG("Enter PM standby!\n");
//    DelayMs(10);
//
//    usb_disable();

    PM_DBG("Enter PM standby!\n");
       DelayMs(10);
           GPIOA_ResetBits(GPIO_Pin_All);
           GPIOB_ResetBits(GPIO_Pin_All);
            DelayMs(10);
            GPIOA_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PU);//pa0,1,4,5,6��������  pa13��������     ,pb11.pb10��������pb12��������
            GPIOB_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PU);//PB10,-15����Ϊ200ua//0-5wei63.8,6-9,16,17wei63.8//
               GPIOB_ModeCfg( GPIO_Pin_14, GPIO_ModeOut_PP_5mA);
               GPIOB_ResetBits(GPIO_Pin_14);
               PFIC_EnableIRQ( GPIO_A_IRQn );
          DelayMs(2);
          LowPower_Shutdown(0); //ȫ���ϵ磬���Ѻ�λ

//
//
//       DelayMs(2);
//       LowPower_Shutdown(0); //ȫ���ϵ磬���Ѻ�λ
//
//
//       PM_DBG("Enter PM standby!\n");
//         DelayMs(10);

//     usb_disable();
//     PFIC_EnableIRQ( GPIO_A_IRQn );
//     RstAllPins();
//     LowPower_Shutdown(0);

}

// 新增深度下电模式函数
void pm_goto_powerdown(void)
{
    PM_DBG("Enter PM powerdown mode!\n");
    DelayMs(10);

    // 断开所有连接
    if(device_mode == MODE_BLE) {
        // 断开BLE连接 - 使用正确的函数名
        GAPRole_TerminateLink(GAP_CONNHANDLE_ALL);
        DelayMs(100);  // 等待断开完成
    }

    // 关闭USB和RF (如果有相应的禁用函数)
    // usb_disable();
    // rf_dev_uinit();

    // 配置所有GPIO为低功耗状态
    GPIOA_ResetBits(GPIO_Pin_All);
    GPIOB_ResetBits(GPIO_Pin_All);
    DelayMs(10);

    // 配置大部分GPIO为下拉输入以降低功耗
    GPIOA_ModeCfg(GPIO_Pin_All, GPIO_ModeIN_PD);
    GPIOB_ModeCfg(GPIO_Pin_All, GPIO_ModeIN_PD);

    // 配置E、Q、R三个唤醒按键为上拉输入，并启用中断
    // E键 (PA0)
    GPIOA_ModeCfg(GPIO_Pin_0, GPIO_ModeIN_PU);
    GPIOA_ITModeCfg(GPIO_Pin_0, GPIO_ITMode_FallEdge);  // 下降沿触发
    PFIC_EnableIRQ(GPIO_A_IRQn);

    // Q键 (PB8) 和 R键 (PB9)
    GPIOB_ModeCfg(GPIO_Pin_8 | GPIO_Pin_9, GPIO_ModeIN_PU);
    GPIOB_ITModeCfg(GPIO_Pin_8 | GPIO_Pin_9, GPIO_ITMode_FallEdge);  // 下降沿触发
    PFIC_EnableIRQ(GPIO_B_IRQn);

    PM_DBG("Configured wakeup pins: PA0(E), PB8(Q), PB9(R)\n");

    // 设置唤醒标志，表示下次启动是从睡眠唤醒
    set_wakeup_source(WAKEUP_SOURCE_SLEEP);

    DelayMs(2);

    // 进入深度睡眠模式，保持部分RAM
    LowPower_Shutdown(RB_PWR_RAM2K); // 保持2KB RAM，包含唤醒信息
}

uint16_t pm_event(uint8_t task_id, uint16_t events)
{
    if (events & SYS_EVENT_MSG) {
        tmos_event_hdr_t *pMsg;

        if ((pMsg = (tmos_event_hdr_t *)tmos_msg_receive(pm_task_id)) != NULL) {
            pm_tmos_msg((tmos_event_hdr_t *) pMsg);
            // Release the TMOS message
            tmos_msg_deallocate((uint8_t *)pMsg);
        }
        // return unprocessed events
        return (events ^ SYS_EVENT_MSG);
    }



    if(events & PM_ENTER_IDLE_EVENT) {
        PM_DBG("1 minute timeout - entering powerdown mode\n");

        // 1分钟无按键操作，直接进入深度下电模式
        pm_goto_powerdown();

        return (events ^ PM_ENTER_IDLE_EVENT);
    }

    if(events & PM_ENTER_STANDBY_EVENT) {
        pm_goto_standby();

        return (events ^ PM_ENTER_STANDBY_EVENT);
    }

    // PM_ENTER_POWERDOWN_EVENT 处理已移除，直接在PM_ENTER_IDLE_EVENT中调用pm_goto_powerdown()

    return 0;
}

void pm_task_init(void)
{
    pm_task_id = TMOS_ProcessEventRegister(pm_event);
    if(device_mode == MODE_USB || device_mode == MODE_TSET){
        // USB和测试模式不启用下电模式
        pm_start_working(PM_TIMEOUT_FOREVER, PM_TIMEOUT_FOREVER);
    } else {
        // BLE和RF模式：1分钟无按键后进入深度下电模式
        pm_start_working(PM_WORKING_TIMEOUT, PM_TIMEOUT_FOREVER);
    }
}
#else
void pm_task_init(void)
{

}
bool pm_is_in_idle(void)
{

    return false;
}

void pm_start_working(int working_timeout, int idle_timeout)
{

}
void pm_goto_standby(void)
{

}
#endif /* HAL_SLEEP */
