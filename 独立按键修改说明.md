# 独立按键修改说明

## 修改概述
将PA0、PB8、PB9三个引脚从矩阵扫描中分离出来，作为独立的按键检测引脚，分别对应E、Q、R键。

## 修改的引脚功能

### 原始功能 → 新功能
- **PA0**: 矩阵扫描行引脚(Key_S0) → 独立按键E
- **PB8**: 矩阵扫描列引脚(IOmap[8]) → 独立按键Q  
- **PB9**: 矩阵扫描列引脚(IOmap[9]) → 独立按键R

### 独立按键映射
| 引脚 | 按键 | 索引 | HID键值 |
|------|------|------|---------|
| PA0  | E键  | 200  | 0x08    |
| PB8  | Q键  | 201  | 0x14    |
| PB9  | R键  | 202  | 0x15    |

## 修改的文件

### 1. drivers/key_scan/keyscan.h
**新增定义:**
```c
// 独立按键定义
#define Key_E                       (R32_PA_PIN&GPIO_Pin_0)  // E键 (PA0)
#define Key_Q                       (R32_PB_PIN&GPIO_Pin_8)  // Q键 (PB8)
#define Key_R                       (R32_PB_PIN&GPIO_Pin_9)  // R键 (PB9)

// 独立按键索引定义
#define INDEPENDENT_KEY_E_INDEX     200  // E键索引
#define INDEPENDENT_KEY_Q_INDEX     201  // Q键索引  
#define INDEPENDENT_KEY_R_INDEX     202  // R键索引
```

**新增函数声明:**
```c
void scanIndependentKeys(uint8_t *pbuf, uint8_t *key_num);
```

### 2. drivers/key_scan/key_table.h
**新增独立按键映射表:**
```c
// 独立按键映射表
const uint8_t independent_key_table[] = {
    0x08,  // E键 (PA0) - 索引200
    0x14,  // Q键 (PB8) - 索引201
    0x15   // R键 (PB9) - 索引202
};

extern const uint8_t independent_key_table[];
```

### 3. drivers/key_scan/keyscan.c

#### 3.1 GPIO初始化修改
- PA0从矩阵扫描行引脚改为独立按键输入引脚
- PB8、PB9从矩阵扫描列引脚改为独立按键输入引脚
- 移除了PA0、PB8、PB9的矩阵扫描配置

#### 3.2 IOmap数组修改
```c
static const uint32_t IOmap[] = {
    1 << 4, 1 << 5, 1 << 6, 1 << 7, 1 << 14, 1 << 15, 1 << 16, 
    1 << 17, 0, 0, 1 << 8, 1 << 18, 1 << 19, 1 << 20,  // 索引8,9设为0
    1 << 21, 1 << 22, 1 << 23
};
```

#### 3.3 新增独立按键扫描函数
```c
void scanIndependentKeys(uint8_t *pbuf, uint8_t *key_num)
{
    uint8_t KeyNum = *key_num;
    
    // 扫描E键 (PA0)
    if (Key_E == 0) {
        pbuf[KeyNum++] = INDEPENDENT_KEY_E_INDEX;
    }
    
    // 扫描Q键 (PB8) 
    if (Key_Q == 0) {
        pbuf[KeyNum++] = INDEPENDENT_KEY_Q_INDEX;
    }
    
    // 扫描R键 (PB9)
    if (Key_R == 0) {
        pbuf[KeyNum++] = INDEPENDENT_KEY_R_INDEX;
    }
    
    *key_num = KeyNum;
}
```

#### 3.4 矩阵扫描修改
- 移除了PA0(Key_S0)的行扫描
- 在列扫描中跳过无效的IOmap条目
- 在keyScan函数最后调用独立按键扫描
- 修改了按键弹起等待逻辑，排除PA0

### 4. drivers/key_scan/key_parse.c

#### 4.1 按键映射函数修改
```c
void keymap_to_keybuf8(uint8_t *index, uint8_t *keyVal, uint8_t len)
{
    for (int i = 0, idx = 0; i < len; i++)
    {
        uint8_t hid_value = 0;
        
        // 检查是否是独立按键
        if (index[i] >= INDEPENDENT_KEY_E_INDEX && index[i] <= INDEPENDENT_KEY_R_INDEX)
        {
            hid_value = independent_key_table[index[i] - INDEPENDENT_KEY_E_INDEX];
        }
        else if (index[i] < sizeof(key8_table)/sizeof(key8_table[0]))
        {
            hid_value = key8_table[index[i]];
        }
        
        if (hid_value != 0)
        {
            keyVal[2 + idx++] = hid_value;
        }
    }
}
```

## 工作原理

### 1. 矩阵扫描
- 现在只扫描PA1-PA5作为行引脚
- 列扫描跳过PB8和PB9，避免干扰独立按键

### 2. 独立按键扫描
- 在每次矩阵扫描完成后，额外扫描PA0、PB8、PB9
- 使用特殊的索引值(200-202)来标识独立按键

### 3. 按键映射
- 独立按键使用单独的映射表`independent_key_table[]`
- 在按键解析时根据索引范围选择对应的映射表

## 优势

1. **资源利用**: 充分利用了未使用的GPIO引脚
2. **兼容性**: 不影响现有矩阵按键的正常工作
3. **扩展性**: 可以轻松添加更多独立按键
4. **性能**: 独立按键扫描简单高效，不需要复杂的矩阵扫描逻辑

## 注意事项

1. 独立按键使用上拉输入模式，按下时为低电平
2. 独立按键的去抖动依然通过原有的去抖动机制实现
3. 独立按键索引(200-202)不会与矩阵按键索引冲突
4. 修改后的代码保持了原有的扫描频率和时序
