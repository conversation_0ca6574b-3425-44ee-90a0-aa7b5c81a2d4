# 编译错误修复说明

## 问题描述
编译时出现错误：
```
error: 'INDEPENDENT_KEY_E_INDEX' undeclared (first use in this function)
error: 'INDEPENDENT_KEY_R_INDEX' undeclared (first use in this function)
```

## 问题原因
`INDEPENDENT_KEY_E_INDEX`、`INDEPENDENT_KEY_Q_INDEX`、`INDEPENDENT_KEY_R_INDEX`这些常量定义在`keyscan.h`中，但`key_parse.c`文件无法正确访问到这些定义。

## 解决方案

### 1. 将常量定义移动到合适的位置
将独立按键索引常量从`keyscan.h`移动到`key_parse.h`中：

**drivers/key_scan/key_parse.h:**
```c
// 独立按键索引定义
#define INDEPENDENT_KEY_E_INDEX     200  // E键索引
#define INDEPENDENT_KEY_Q_INDEX     201  // Q键索引  
#define INDEPENDENT_KEY_R_INDEX     202  // R键索引
```

### 2. 保持头文件包含关系清晰
**drivers/key_scan/key_parse.c:**
```c
#include "key_parse.h"    // 包含独立按键索引常量
#include "key_table.h"    // 包含independent_key_table声明
#include "CH58x_common.h"
```

**drivers/key_scan/keyscan.h:**
```c
// 只保留GPIO引脚定义
#define Key_E    (R32_PA_PIN&GPIO_Pin_0)  // E键 (PA0)
#define Key_Q    (R32_PB_PIN&GPIO_Pin_8)  // Q键 (PB8)
#define Key_R    (R32_PB_PIN&GPIO_Pin_9)  // R键 (PB9)
```

### 3. 确保声明和定义匹配
**drivers/key_scan/key_table.h:**
```c
// 独立按键映射表
const uint8_t independent_key_table[] = {
    0x08,  // E键 (PA0) - 索引200
    0x14,  // Q键 (PB8) - 索引201
    0x15   // R键 (PB9) - 索引202
};

// 独立按键相关声明
extern const uint8_t independent_key_table[];
```

## 修改后的文件结构

### 头文件依赖关系
```
key_parse.c
├── key_parse.h (包含独立按键索引常量)
├── key_table.h (包含independent_key_table声明)
└── CH58x_common.h

keyscan.c  
├── keyscan.h (包含GPIO引脚定义)
├── key_parse.h (包含独立按键索引常量)
└── 其他头文件...
```

### 常量定义位置
- **GPIO引脚定义**: `keyscan.h`
- **独立按键索引**: `key_parse.h` 
- **按键映射表**: `key_table.h`

## 验证编译
修改完成后，编译应该能够通过：
```bash
make drivers/key_scan/key_parse.o
```

## 功能验证
修改后的代码应该能够：
1. 正确编译通过
2. 支持独立按键E、Q、R的扫描
3. 不影响原有矩阵按键的功能
4. 正确映射独立按键到对应的HID键值

## 注意事项
1. 确保所有使用独立按键索引常量的文件都包含了`key_parse.h`
2. 确保`independent_key_table`的定义和声明保持一致
3. 避免循环包含头文件的问题
