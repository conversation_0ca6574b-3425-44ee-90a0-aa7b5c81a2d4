/********************************** (C) COPYRIGHT *******************************
 * File Name          : main.c
 * Author             : WCH
 * Version            : V1.1
 * Date               : 2020/08/06
 * Description        : 外设从机应用主函数及任务系统初始化
 *******************************************************************************/

/******************************************************************************/
/* 头文件包含 */
#include "CH58x_common.h"
#include "HAL/config.h"
#include "HAL_FLASH/include/easyflash.h"
#include "device_config.h"
#include "RingBuffer/lwrb.h"
#include "config.h"
#include "RF_PHY/rf_sync.h"
#include "I2C/myi2c.h"
#include "power_led/sleep_manager.h"


bool isUSBinsert = false;
/*********************************************************************
 * GLOBAL TYPEDEFS
 */
__attribute__((aligned(4)))   u32 MEM_BUF[BLE_MEMHEAP_SIZE / 4];

uint8_t MacAddr[6] = { 0x84, 0xC2, 0xE4, 0x13, 0x33, 0x44 };

/*******************************************************************************
 * Function Name  : Main_Circulation
 * Description    : 主循环
 * Input          : None
 * Output         : None
 * Return         : None
 *******************************************************************************/
__attribute__((section(".highcode")))
void Main_Circulation() {
    while (1) {
//        rf_sync();
        TMOS_SystemProcess();
    }
}

/*******************************************************************************
 * Function Name  : main
 * Description    : 主函数
 * Input          : None
 * Output         : None
 * Return         : None
 *******************************************************************************/
int main(void)
{
    // ===== 步骤1: 基础系统初始化 =====
    PowerMonitor(ENABLE, LPLevel_2V5);
    PFIC_EnableIRQ(WDOG_BAT_IRQn);
#if (defined (DCDC_ENABLE)) && (DCDC_ENABLE == TRUE)
    PWR_DCDCCfg(ENABLE);
#endif

    // ===== 步骤2: 读取复位原因并决定启动模式 =====
    uint8_t reset_flag = get_reset_flag();

    PRINT("Reset flag: 0x%02X\n", reset_flag);

    if (is_power_on_reset()) {
        // ----------------------------------------------------
        // 首次上电或电源开关重启 - 立即进入睡眠模式
        // ----------------------------------------------------
        PRINT("=== POWER ON RESET - ENTERING SLEEP MODE ===\n");

        // 初始化LED（用于可能的指示）
        power_led_init();

        // 配置唤醒按键
        configure_wakeup_keys();

        // 进入下电模式
        enter_shutdown_mode();

        // 注意：程序执行到这里就会停止，直到被唤醒并复位

    } else if (is_shutdown_wakeup_reset()) {
        // ----------------------------------------------------
        // 从下电模式唤醒 - 执行正常工作逻辑
        // ----------------------------------------------------
        PRINT("=== SHUTDOWN WAKEUP RESET - NORMAL OPERATION ===\n");

        // 初始化LED并显示唤醒指示
        power_led_init();
        power_led_blink_once();  // 闪烁一次表示唤醒

        // 继续正常的系统初始化...

    } else {
        // ----------------------------------------------------
        // 其他复位原因 - 进入正常工作模式
        // ----------------------------------------------------
        PRINT("=== OTHER RESET (0x%02X) - NORMAL OPERATION ===\n", reset_flag);

        // 初始化LED
        power_led_init();

        // 继续正常的系统初始化...
    }





#if (defined (HAL_SLEEP)) && (HAL_SLEEP == TRUE)
    GPIOA_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PD);
    GPIOB_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PD);
#endif

#ifdef DEBUG
    GPIOA_SetBits(bTXD1);
    GPIOA_ModeCfg(bTXD1, GPIO_ModeOut_PP_5mA);
    UART1_DefInit();
    UART1_BaudRateCfg(115200);
#endif
    DEBUG_Init();
#ifdef CONFIG_RF_DEBUG_GPIO
    GPIOA_SetBits(GPIO_Pin_0 | GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_6);
    GPIOA_ModeCfg(GPIO_Pin_0 | GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_6, GPIO_ModeOut_PP_5mA);
#endif

//    GPIOB_ResetBits(GPIO_Pin_23);
//        GPIOB_ModeCfg( GPIO_Pin_23, GPIO_ModeOut_PP_5mA);

//    GPIOA_ModeCfg(GPIO_Pin_15, GPIO_ModeIN_PD);
//    GPIOA_SetBits(GPIO_Pin_14);
//    GPIOA_ModeCfg( GPIO_Pin_14, GPIO_ModeOut_PP_5mA);


//    if(GPIOA_ReadPortPin(GPIO_Pin_15)){
//        GPIOA_ResetBits(GPIO_Pin_14);
//        isUSBinsert = true;
//        PRINT("USB insert\n");
//    } else{
//        GPIOA_ModeCfg( GPIO_Pin_14, GPIO_ModeIN_Floating);
//        isUSBinsert = false;
//        PRINT("NO USB\n");
//    }





    if (easyflash_init() != SUCCESS) {
        LOG_INFO("Date Flash init error!");
    }
    ef_print_env();
    ReadDeviceInfo("all");  //must process after easyflash_init()

    ring_buffer_init();

    LOG_INFO("device id:%#x", device_bond.ID_Num);

    Mode_Init(device_mode);

    tmos_start_task( halTaskID, HAL_ADC_EVENT, MS1_TO_SYSTEM_TIME(6*1000));   //1min 检测一次采样电压

    Main_Circulation();
}

__HIGH_CODE
void WDOG_BAT_IRQHandler(void)
{
    FLASH_ROM_SW_RESET();

	uint32_t mcause;
	__asm__ volatile("csrr %0, mcause" : "=r" (mcause));

	uint32_t mtval;
	__asm__ volatile("csrr %0, mtval" : "=r" (mtval));

	mcause &= 0x1f;

    PRINT("WDOG_BAT_IRQHandler\n");
	PRINT("mcause: %ld\n", mcause);
	PRINT("mtval: %lx\n", mtval);
	DelayMs(10);

    while (1){
        static uint32_t i = 0;
        if(R8_BAT_STATUS & RB_BAT_STAT_LOW){
            i = 0;
        } else {
            i++;
            if(i > 100){
                break;
            }
        }
        DelayMs(1);
    }

    SYS_ResetExecute();
    __builtin_unreachable();
}
//更改记录：将电量检测打开#define HAL_ADC true,hidkbd.c里关闭了名称{1618}//scanRspData[]里改为0x45,,HidEmu_Init()里的电池设置关闭，
//uint8 battMeasure( void )去掉前面的static，电池等级管理处改了。HID_RPT_ID_KEY_IN，GPIO_Pin_23,RST,CH582不用RST,CH582_RST_to_GPIO
//GPIO_Pin_2,下载要去掉复位脚，HID_RPT_ID_KEY_IN，conn param updata，ble_chan_1: long,  B和ALT R换位PB12
//GPIO_Pin_12Initialized..battery low，关闭了识别USB，Enter pm idle，Status:hidEmu_ProcessTMOSMsg
//conn param updata:WAKE,HID_RPT_ID_KEY_IN,IRQHandler，IRQ GPIO_Pin_6,Enter pm idle,param updata:
//conn param updata:Enter PM standby!
//Fn 空闲模式 Idle：1.6mASLEEPDEEP设置 POWER_PLAN，  GPIOA_ITModeCfg,GPIOA_ClearITFlagBit


///*DataFlash初始化*/
//    GPIOA_SetBits(GPIO_Pin_All);
//    GPIOA_ResetBits(GPIO_Pin_All);
//     DelayMs(10);
//
////        usb_disable();
//     GPIOA_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PU);//pa0,1,4,5,6必须上拉  pa13必须下拉     ,pb11必须下拉pb10必须下拉pb12必须上拉
//     GPIOB_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PD);//PB10,-15上拉为200ua//0-5wei63.8,6-9,16,17wei63.8//
////         GPIOA_ModeCfg(
////                 GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_2 | GPIO_Pin_3 | GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_6,
////                 GPIO_ModeIN_PU);
////        GPIOB_ModeCfg(
////                        GPIO_Pin_12,
////                       GPIO_ModeIN_PU);
//
////        PFIC_EnableIRQ( GPIO_A_IRQn );
////    PRINT("shut down mode sleep \n");
// DelayMs(5000);
// PRINT("shut down mode sleep \n");
//    DelayMs(2);
//    LowPower_Shutdown(0); //全部断电，唤醒后复位




/*DataFlash初始化*/
//
//DelayMs(10);
//
////        usb_disable();
//GPIOA_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PU);//pa0,1,4,5,6必须上拉  pa13必须下拉     ,pb11必须下拉pb10必须下拉pb12必须上拉
//GPIOB_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PD);//PB10,-15上拉为200ua//0-5wei63.8,6-9,16,17wei63.8//
// GPIOA_ModeCfg(
//         GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_2 | GPIO_Pin_3 | GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_6,
//         GPIO_ModeIN_PU);
//GPIOB_ModeCfg(
//                GPIO_Pin_12,
//               GPIO_ModeIN_PU);
//
////        PFIC_EnableIRQ( GPIO_A_IRQn );
////    PRINT("shut down mode sleep \n");
//DelayMs(5000);
//PRINT("shut down mode sleep \n");
//DelayMs(2);
//LowPower_Shutdown(0); //全部断电，唤醒后复位

////    PM_DBG("Enter PM standby!\n");
//       DelayMs(10);
//           GPIOA_ResetBits(GPIO_Pin_All);
//           GPIOB_ResetBits(GPIO_Pin_All);
//            DelayMs(10);
//            GPIOA_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PU);//pa0,1,4,5,6必须上拉  pa13必须下拉     ,pb11.pb10必须下拉pb12必须上拉
//            GPIOB_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PU);//PB10,-15上拉为200ua//0-5wei63.8,6-9,16,17wei63.8//
//               GPIOB_ModeCfg( GPIO_Pin_14, GPIO_ModeOut_PP_5mA);
//               GPIOB_ResetBits(GPIO_Pin_14);
//               PFIC_EnableIRQ( GPIO_A_IRQn );
//          DelayMs(2);
//          LowPower_Shutdown(0); //全部断电，唤醒后复位


/******************************** endfile @ main ******************************/
