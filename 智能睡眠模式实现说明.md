# 智能睡眠模式实现说明

## 功能概述
基于CH58x芯片的复位状态寄存器，实现智能启动逻辑：
- **首次上电**：立即进入睡眠模式，等待按键唤醒
- **按键唤醒**：LED闪烁一次，然后正常工作
- **蓝牙断开**：进入睡眠前自动断开蓝牙连接

## 核心原理

### 复位标志检测
根据CH58x手册第25页，R8_RESET_STATUS寄存器记录复位原因：
- `0x01` (001b): 上电复位 (RPOR - Real Power-On Reset)
- `0x05` (101b): 下电模式唤醒复位 (GRWSM - Global Reset by Waking under Shutdown Mode)

### 智能启动逻辑
```c
int main(void)
{
    // 基础系统初始化
    PowerMonitor(ENABLE, LPLevel_2V5);
    PFIC_EnableIRQ(WDOG_BAT_IRQn);
    
    // 读取复位原因
    uint8_t reset_flag = get_reset_flag();
    
    if (is_power_on_reset()) {
        // 首次上电 - 立即睡眠
        configure_wakeup_keys();
        enter_shutdown_mode();
    } else if (is_shutdown_wakeup_reset()) {
        // 按键唤醒 - 正常工作
        power_led_blink_once();  // 唤醒指示
        // 继续正常初始化...
    } else {
        // 其他复位 - 正常工作
        // 继续正常初始化...
    }
}
```

## 实现细节

### 1. 文件结构
```
drivers/power_led/
├── sleep_manager.h      # 睡眠管理头文件
└── sleep_manager.c      # 睡眠管理实现
```

### 2. 核心函数

#### 复位检测函数
```c
uint8_t get_reset_flag(void);           // 获取复位标志
bool is_power_on_reset(void);           // 判断是否上电复位
bool is_shutdown_wakeup_reset(void);    // 判断是否唤醒复位
```

#### 睡眠管理函数
```c
void configure_wakeup_keys(void);       // 配置唤醒按键
void enter_shutdown_mode(void);         // 进入下电模式
```

#### LED控制函数
```c
void power_led_init(void);              // 初始化LED
void power_led_blink_once(void);        // 闪烁一次
```

### 3. 唤醒按键配置
支持三个按键唤醒：
- **E键 (PA0)**: 上拉输入，下降沿触发
- **Q键 (PB8)**: 上拉输入，下降沿触发  
- **R键 (PB9)**: 上拉输入，下降沿触发

### 4. 低功耗优化
进入睡眠前的优化措施：
- 关闭所有LED
- 配置大部分GPIO为下拉输入
- 断开蓝牙连接
- 只保持唤醒按键为上拉输入

## 工作流程

### 1. 首次上电流程
```
上电 → 基础初始化 → 检测复位标志 → 
判断为上电复位 → 配置唤醒按键 → 进入下电模式
```

### 2. 按键唤醒流程
```
按键按下 → GPIO唤醒 → 系统复位 → 基础初始化 → 
检测复位标志 → 判断为唤醒复位 → LED闪烁指示 → 
正常系统初始化 → 蓝牙连接 → 正常工作
```

### 3. 自动睡眠流程
```
正常工作 → 检测无活动 → 断开蓝牙 → 
配置唤醒按键 → 进入下电模式
```

## 优势特点

### 1. 彻底解决无限循环
- 使用硬件复位标志，100%可靠区分上电和唤醒
- 避免了基于RAM的方案可能出现的数据丢失问题

### 2. 极致省电
- 上电后立即睡眠，无多余初始化
- 下电模式功耗最低（微安级别）
- 只在真正需要工作时才初始化外设

### 3. 用户体验友好
- 上电无指示，静默进入睡眠
- 唤醒有LED闪烁确认
- 响应迅速，唤醒后立即可用

### 4. 代码结构清晰
- 启动逻辑与功能代码完全分离
- 原有键盘功能代码无需修改
- 便于维护和调试

## 调试信息

### 复位标志含义
- `0x01`: 上电复位 - 设备会进入睡眠
- `0x05`: 唤醒复位 - 设备会正常工作
- 其他值: 异常复位 - 设备会正常工作

### 串口输出示例
```
Reset flag: 0x01
=== POWER ON RESET - ENTERING SLEEP MODE ===
Configuring wakeup keys...
Wakeup keys configured: PA0(E), PB8(Q), PB9(R)
Preparing to enter shutdown mode...
Entering shutdown mode now...
```

```
Reset flag: 0x05
=== SHUTDOWN WAKEUP RESET - NORMAL OPERATION ===
[LED闪烁一次]
[继续正常系统初始化...]
```

## 注意事项

1. **构建配置**: 确保`drivers/power_led`目录已添加到构建系统
2. **GPIO中断**: 现有的GPIO中断处理函数会自动处理唤醒
3. **蓝牙断开**: 需要在进入睡眠前添加蓝牙断开代码
4. **调试模式**: 睡眠模式下串口会停止，这是正常现象

## 扩展功能

### 1. 自动睡眠定时器
可以添加无活动检测，在一定时间后自动进入睡眠：
```c
void check_for_inactivity_and_go_to_sleep(void) {
    if (no_activity_time > SLEEP_TIMEOUT) {
        enter_shutdown_mode();
    }
}
```

### 2. 唤醒原因检测
可以通过GPIO中断标志检测是哪个按键唤醒的设备。

### 3. 睡眠计数器
可以记录睡眠和唤醒次数，用于功耗分析。
