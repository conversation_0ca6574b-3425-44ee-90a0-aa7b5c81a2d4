/*
 * Copyright (c) 2022 Nanjing Qinheng Microelectronics Co., Ltd.
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#ifndef DRIVERS_KEY_SCAN_KEY_TABLE_H
#define DRIVERS_KEY_SCAN_KEY_TABLE_H

#include <stdint.h>

//const uint8_t key8_table[] = //Ĭ�ϵļ����
//{
//    //        R0   R1   R2   R3   R4   R5
//    0x00, 0x29, 0x35, 0x2b, 0x39, 0x02, 0x01, // c0                 //esc           ~`              tab         caps-lock  shift-L      ctr-l
//    0x00, 0x3a, 0x1e, 0x14, 0x04, 0x1d, 0x08, // c1                 //F1            1!              Q           A          Z            win-l
//    0x00, 0x3b, 0x1f, 0x1a, 0x16, 0x1b, 0x04, // c2                 //f2            2@              W           S          X            alt-l
//    0x00, 0x3c, 0x20, 0x08, 0x07, 0x06, 0x2c, // c3                 //F3            3#              E           D          C            Space
//    0x00, 0x3d, 0x21, 0x15, 0x24, 0x19, 0x40, // c4                 //F4            4$              R          7&          V            alt-r
//    0x00, 0x3e, 0x22, 0x17, 0x0a, 0x05, 0xfe, // c5                 //F5            5%              T           G          B            Fn
//
//    0x00, 0x3f, 0x23, 0x1c, 0x0b, 0x11, 0x10, // c6                 //F6            6^              Y           H          N
//    0x00, 0x40, 0x09, 0x18, 0x0d, 0x10, 0x80, // c7                 //F7            7&              U           J          M            win-r
//    0x00, 0x41, 0x25, 0x0c, 0x0e, 0x36, 0x10, // c8                 //F8            8*              I           K          <,           ctr-r
//    0x00, 0x42, 0x26, 0x12, 0x0f, 0x37, 0x00, // c9                 //F9            9               O           L          >.
//    0x00, 0x43, 0x27, 0x13, 0x33, 0x38, 0x00, // c10                //F10           0               P           ;:         /?
//    0x00, 0x44, 0x2d, 0x2f, 0x34, 0x20, 0x00, // c11                //F11           -_              [{          '"         shift-r
//    0x00, 0x45, 0x2e, 0x30, 0x31, 0x00, 0x00, // c12                //F12           =+              ]}          \|
//
//    0x00, 0x00, 0x2a, 0x00, 0x28, 0x00, 0x00, // c13                //0x00          Backspace                   Enter-R
//    0x00, 0x46, 0x49, 0x4c, 0x00, 0x00, 0x50, // c14                //Print-screen  Insert          Delete      0x00,       0x00,       ��
//    0x00, 0x47, 0x4a, 0x4d, 0x00, 0x52, 0x51, // c15                //Scroll-Lock   Home            End         0x00,       ��                           ��
//    0x00, 0x48, 0x4b, 0x4e, 0x00, 0x00, 0x4f, // c16                //Pause         Page-Up         Page-Down   0x00        0x00        ��
//    0x00, 0x00, 0x53, 0x5f, 0x5c, 0x59, 0x00, // c17                //Backlight     Num-lock        7HOME       4(С����)   1End       0x00
//    0x00, 0x00, 0x54, 0x60, 0x5d, 0x5a, 0x62, // c18                //Locking       /               8(С����)   5(С����)   2(С����)   0Ins
//    0x00, 0x00, 0x55, 0x61, 0x5e, 0x5b, 0x63, // c19                //0x00          *               9Pgup       6(С����)   3PgDn       =del
//    0x00, 0x00, 0x56, 0x57, 0x00, 0x00, 0x58  // c20                 //0x00          -               +           0x00        0x00        Enter-R2
//};
const uint8_t key8_table[] = //自定义按键映射表
{
    //        R0   R1   R2   R3   R4   R5   R6
    0x00, 0x00, 0x00, 0x1b, 0x00, 0x00, 0x01, // c0  - 索引1-6     //无     无     X(3)   无     无     CTRL(6)
    0x00, 0x00, 0x1e, 0x00, 0x00, 0x04, 0x00, // c1  - 索引8-13    //无     1(9)   无     无     A(12)  无
    0x00, 0x00, 0x1f, 0x1a, 0x00, 0x16, 0x07, // c2  - 索引15-20   //无     2(16)  W(17)  无     S(19)  D(20)
    0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x2c, // c3  - 索引22-27   //无     3(23)  无     无     无     空格(27)
    0x00, 0x00, 0x21, 0x00, 0x00, 0x00, 0x00, // c4  - 索引29-34   //无     4(30)  无     无     无     无
    0x00, 0x00, 0x22, 0x19, 0x00, 0x00, 0x00, // c5  - 索引36-41   //无     5(37)  V(38)  无     无     无

    0x00, 0x00, 0x23, 0x00, 0x00, 0x00, 0x00, // c6  - 索引43-48   //无     6(44)  无     无     无     无
    0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x02, // c7  - 索引50-55   //无     无     无     无     M(54)  SHIFT(55)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // c8  - 索引57-62   //无     无     无     无     无     无
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // c9  - 索引64-69   //无     无     无     无     无     无
    0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x00, // c10 - 索引71-76   //无     无     无     无     G(75)  无
    0x00, 0x00, 0x09, 0x17, 0x00, 0x00, 0x00, // c11 - 索引78-83   //无     F(79)  T(80)  无     无     无
    0x00, 0x00, 0x24, 0x05, 0x00, 0x00, 0x00, // c12 - 索引85-90   //无     7(86)  B(87)  无     无     无

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // c13 - 索引92-97   //无     无     无     无     无     无
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, // c14 - 索引99-104  //无     无     无     无     无     C(104)
    0x00, 0x00, 0x00, 0x1d, 0x00, 0x00, 0x00, // c15 - 索引106-111 //无     无     Z(108) 无     无     无
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // c16 - 索引113-118 //无     无     无     无     无     无
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // c17 - 索引120-125 //无     无     无     无     无     无
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // c18 - 索引127-132 //无     无     无     无     无     无
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // c19 - 索引134-139 //无     无     无     无     无     无
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00  // c20 - 索引141-146 //无     无     无     无     无     无
};

// 独立按键映射表
const uint8_t independent_key_table[] = {
    0x08,  // E键 (PA0) - 索引200
    0x14,  // Q键 (PB8) - 索引201
    0x15   // R键 (PB9) - 索引202
};

// 独立按键相关声明
extern const uint8_t independent_key_table[];

#endif /* DRIVERS_KEY_SCAN_KEY_TABLE_H */


