/********************************** (C) COPYRIGHT *******************************
* File Name          : RTC.h
* Author             : WCH
* Version            : V1.0
* Date               : 2016/04/12
* Description        : 
*******************************************************************************/



/******************************************************************************/
#ifndef __RTC_H
#define __RTC_H

#ifdef __cplusplus
extern "C"
{
#endif

#include "config.h"

#define  RTC_TIMER_MAX_VALUE                0xa8c00000

#ifdef CLK_OSC32K
#if (CLK_OSC32K==1)
#define FREQ_RTC    32000
#else
#define FREQ_RTC    32768
#endif
#endif /* CLK_OSC32K */


#define CLK_PER_US                  (1.0 / ((1.0 / FREQ_RTC) * 1000 * 1000))
#define CLK_PER_MS                  (CLK_PER_US * 1000)

#define US_PER_CLK                  (1.0 / CLK_PER_US)
#define MS_PER_CLK                  (US_PER_CLK / 1000.0)

#define RTC_TO_US(clk)              ((uint32_t)((clk) * US_PER_CLK))
#define RTC_TO_MS(clk)              ((uint32_t)((clk) * MS_PER_CLK))

#define US_TO_RTC(us)               ((uint32_t)((us) * CLK_PER_US))
#define MS_TO_RTC(ms)               ((uint32_t)((ms) * CLK_PER_MS))

extern u32V RTCTigFlag;
  
/*
 * Initialize time Service.
 */
void HAL_TimeInit( void );

/*
 * System Clock Service.
 */
extern void RTC_SetTignTime( u32 time );  

#ifdef __cplusplus
}
#endif

#endif
