##########################################################################################
#                        Append Compiler Options For Source Files
#
# syntax:
#   <your matcher expr>: <your compiler command>
#
# examples:
#   'main.cpp':           --cpp11 -Og ...
#   'src/*.c':            -gnu -O2 ...
#   'src/lib/**/*.cpp':   --cpp11 -Os ...
#   '!Application/*.c':   -O0
#   '**/*.c':             -O2 -gnu ...
#
# For more syntax, please refer to: https://www.npmjs.com/package/micromatch
#
##########################################################################################

version: '1.0'

#
# for source files with filesystem paths
#
files:
#   './test/**/*.c': --c99

#
# for source files with virtual paths
#
virtualPathFiles:
#   'virtual_folder/**/*.c': --c99

