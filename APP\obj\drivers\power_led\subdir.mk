################################################################################
# MRS Version: {"version":"1.8.5","date":"2023/05/22"}
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
D:/DESK/mouse3.0/�����ļ�/code_1/drivers/power_led/power_led.c 

OBJS += \
./drivers/power_led/power_led.o 

C_DEPS += \
./drivers/power_led/power_led.d 


# Each subdirectory must supply rules for building sources it contributes
drivers/power_led/power_led.o: D:/DESK/mouse3.0/�����ļ�/code_1/drivers/power_led/power_led.c
	@	@	riscv-none-embed-gcc -march=rv32imac -mabi=ilp32 -mcmodel=medany -msmall-data-limit=8 -mno-save-restore -Os -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-common  -g -DCONFIG_RISCV -DCLK_OSC32K=1 -DLOG -DDEBUG=1 -I"D:\DESK\mouse3.0\�����ļ�\code_1\StdPeriphDriver\inc" -I"D:\DESK\mouse3.0\�����ļ�\code_1\subsys\BLE" -I"D:\DESK\mouse3.0\�����ļ�\code_1\subsys\HAL" -I"D:\DESK\mouse3.0\�����ļ�\code_1\subsys\BLE\profile" -isystem"../../boards" -isystem"../../LIB" -isystem"../../drivers" -isystem"../../include" -isystem"../../kernel" -isystem"../../subsys" -isystem"../../soc" -isystem"../../StdPeriphDriver" -isystem"../src" -isystem"../../test" -std=gnu99 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$(@)" -c -o "$@" "$<"
	@	@

