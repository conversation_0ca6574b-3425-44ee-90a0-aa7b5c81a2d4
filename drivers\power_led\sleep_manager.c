#include "sleep_manager.h"

// 读取复位标志
uint8_t get_reset_flag(void)
{
    return R8_RESET_STATUS & RESET_FLAG_MASK;
}

// 判断是否为上电复位
bool is_power_on_reset(void)
{
    return (get_reset_flag() == RESET_FLAG_RPOR);
}

// 判断是否为下电模式唤醒复位
bool is_shutdown_wakeup_reset(void)
{
    return (get_reset_flag() == RESET_FLAG_GRWSM);
}

// LED控制函数
void power_led_init(void)
{
    // 配置PB12为推挽输出
    GPIOB_ModeCfg(POWER_LED_PIN, GPIO_ModeOut_PP_5mA);
    power_led_off();  // 初始状态为关闭
}

void power_led_on(void)
{
    GPIOB_SetBits(POWER_LED_PIN);
}

void power_led_off(void)
{
    GPIOB_ResetBits(POWER_LED_PIN);
}

// 唤醒指示：闪烁一次
void power_led_blink_once(void)
{
    power_led_on();
    DelayMs(200);
    power_led_off();
    DelayMs(200);
}

// 配置唤醒按键
void configure_wakeup_keys(void)
{
    PRINT("Configuring wakeup keys...\n");
    
    // 配置E键 (PA0) - 上拉输入，下降沿唤醒
    GPIOA_ModeCfg(GPIO_Pin_0, GPIO_ModeIN_PU);
    
    // 配置Q键 (PB8) 和 R键 (PB9) - 上拉输入，下降沿唤醒  
    GPIOB_ModeCfg(GPIO_Pin_8 | GPIO_Pin_9, GPIO_ModeIN_PU);
    
    // 清除GPIO中断标志
    GPIOA_ClearITFlagBit(GPIO_Pin_0);
    GPIOB_ClearITFlagBit(GPIO_Pin_8 | GPIO_Pin_9);
    
    // 配置GPIO唤醒源
    PWR_PeriphWakeUpCfg(ENABLE, RB_SLP_GPIO_WAKE, Long_Delay);
    
    PRINT("Wakeup keys configured: PA0(E), PB8(Q), PB9(R)\n");
}

// 进入下电模式
void enter_shutdown_mode(void)
{
    PRINT("Preparing to enter shutdown mode...\n");
    
    // 关闭LED
    power_led_off();
    
    // 配置所有GPIO为低功耗状态
    GPIOA_ResetBits(GPIO_Pin_All);
    GPIOB_ResetBits(GPIO_Pin_All);
    DelayMs(10);
    
    // 配置大部分GPIO为下拉输入以降低功耗
    GPIOA_ModeCfg(GPIO_Pin_All, GPIO_ModeIN_PD);
    GPIOB_ModeCfg(GPIO_Pin_All, GPIO_ModeIN_PD);
    
    // 重新配置唤醒按键（确保不被覆盖）
    configure_wakeup_keys();
    
    // 断开蓝牙连接（如果已连接）
    // 这里可以添加蓝牙断开的代码
    
    DelayMs(50);  // 确保所有配置生效
    
    PRINT("Entering shutdown mode now...\n");
    DelayMs(10);  // 确保串口输出完成
    
    // 进入下电模式，保持RAM以便调试
    LowPower_Shutdown(0);
    
    // 如果执行到这里，说明进入睡眠失败
    PRINT("ERROR: Failed to enter shutdown mode!\n");
    while(1) {
        DelayMs(1000);
        PRINT("Sleep failed, retrying...\n");
    }
}
