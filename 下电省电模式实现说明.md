# 下电省电模式实现说明

## 功能概述
实现了一个深度下电省电模式，设备在1分钟内无按键操作时自动进入下电模式，断开USB和RF供电，断开蓝牙连接，并配置E、Q、R三个按键作为GPIO唤醒源。

## 主要特性

### 1. 自动进入条件
- **触发时间**: 1分钟内无任何按键操作
- **适用模式**: BLE模式和RF模式（USB和测试模式不启用）
- **检测范围**: 包括矩阵按键和独立按键(E、Q、R)

### 2. 下电前操作
- 断开BLE连接 (`GAPRole_TerminateConnection()`)
- 关闭USB和RF模块供电
- 配置所有GPIO为低功耗状态

### 3. 唤醒配置
- **唤醒按键**: E键(PA0)、Q键(PB8)、R键(PB9)
- **触发方式**: 下降沿触发（按键按下）
- **唤醒后**: 系统自动复位重启

## 修改的文件

### 1. subsys/PM/pm_task.h
**新增定义:**
```c
#define PM_WORKING_TIMEOUT              (1000*60)    // 1分钟无按键进入下电模式
#define PM_ENTER_POWERDOWN_EVENT        (1<<3)       // 深度下电模式事件

void pm_goto_powerdown(void);  // 新增深度下电模式函数
```

### 2. subsys/PM/pm_task.c
**新增深度下电函数:**
```c
void pm_goto_powerdown(void)
{
    PM_DBG("Enter PM powerdown mode!\n");
    
    // 断开所有连接
    if(device_mode == MODE_BLE) {
        GAPRole_TerminateConnection();
        DelayMs(100);
    }
    
    // 配置GPIO为低功耗状态
    GPIOA_ResetBits(GPIO_Pin_All);
    GPIOB_ResetBits(GPIO_Pin_All);
    GPIOA_ModeCfg(GPIO_Pin_All, GPIO_ModeIN_PD);
    GPIOB_ModeCfg(GPIO_Pin_All, GPIO_ModeIN_PD);
    
    // 配置唤醒按键
    GPIOA_ModeCfg(GPIO_Pin_0, GPIO_ModeIN_PU);                    // E键
    GPIOA_ITModeCfg(GPIO_Pin_0, GPIO_ITMode_FallEdge);
    PFIC_EnableIRQ(GPIO_A_IRQn);
    
    GPIOB_ModeCfg(GPIO_Pin_8 | GPIO_Pin_9, GPIO_ModeIN_PU);       // Q键、R键
    GPIOB_ITModeCfg(GPIO_Pin_8 | GPIO_Pin_9, GPIO_ITMode_FallEdge);
    PFIC_EnableIRQ(GPIO_B_IRQn);
    
    // 进入深度睡眠
    LowPower_Shutdown(0);
}
```

**修改事件处理:**
- `PM_ENTER_IDLE_EVENT`: 1分钟超时后触发深度下电模式
- `PM_ENTER_POWERDOWN_EVENT`: 执行深度下电操作
- `pm_task_init()`: 使用新的1分钟超时时间

### 3. soc/soc.c
**新增GPIO中断处理函数:**
```c
// GPIO A中断处理函数 - 处理E键(PA0)唤醒
__HIGH_CODE
__INTERRUPT
void GPIOA_IRQHandler(void)
{
    if(GPIOA_ReadITFlagBit(GPIO_Pin_0)) {
        GPIOA_ClearITFlagBit(GPIO_Pin_0);
        PRINT("Wakeup by E key (PA0)\n");
    }
}

// GPIO B中断处理函数 - 处理Q键(PB8)和R键(PB9)唤醒
__HIGH_CODE
__INTERRUPT
void GPIOB_IRQHandler(void)
{
    if(GPIOB_ReadITFlagBit(GPIO_Pin_8)) {
        GPIOB_ClearITFlagBit(GPIO_Pin_8);
        PRINT("Wakeup by Q key (PB8)\n");
    }
    
    if(GPIOB_ReadITFlagBit(GPIO_Pin_9)) {
        GPIOB_ClearITFlagBit(GPIO_Pin_9);
        PRINT("Wakeup by R key (PB9)\n");
    }
}
```

### 4. drivers/key_scan/keyscan.c
**新增电源管理重置逻辑:**
```c
// 如果有按键按下，重置电源管理计时器
if(is_key) {
    pm_start_working(PM_WORKING_TIMEOUT, PM_TIMEOUT_FOREVER);
}
```

## 工作流程

### 1. 正常工作状态
```
按键检测 → 重置1分钟计时器 → 继续正常工作
```

### 2. 进入下电模式
```
1分钟无按键 → PM_ENTER_IDLE_EVENT → PM_ENTER_POWERDOWN_EVENT → 深度下电
```

### 3. 唤醒流程
```
按下E/Q/R键 → GPIO中断触发 → 系统复位 → 重新启动
```

## 技术特点

### 1. 低功耗设计
- **GPIO配置**: 大部分GPIO配置为下拉输入，降低漏电流
- **唤醒GPIO**: 只有E、Q、R三个按键配置为上拉输入并启用中断
- **模块关闭**: 断开USB、RF、BLE等高功耗模块

### 2. 可靠性保证
- **连接断开**: 进入下电前主动断开BLE连接，避免连接异常
- **延时处理**: 断开连接后延时100ms确保操作完成
- **中断清理**: 唤醒时正确清除中断标志位

### 3. 调试支持
- **调试输出**: 提供详细的状态转换和唤醒信息
- **状态跟踪**: 可通过串口监控电源管理状态变化

## 功耗估算

### 1. 正常工作模式
- BLE连接状态: ~10-20mA
- 按键扫描: ~1-2mA
- 总计: ~15-25mA

### 2. 深度下电模式
- 大部分GPIO下拉: ~10-50μA
- 3个唤醒GPIO上拉: ~10-30μA
- 系统最小功耗: ~50-100μA
- **功耗降低**: 约200-500倍

## 使用说明

### 1. 进入下电模式
- 设备正常工作时，1分钟内不操作任何按键
- 系统自动断开连接并进入下电模式
- LED指示灯熄灭，设备进入极低功耗状态

### 2. 唤醒设备
- 按下E键(PA0)、Q键(PB8)或R键(PB9)中的任意一个
- 系统立即复位重启
- 设备恢复到正常工作状态

### 3. 注意事项
- 唤醒后设备会完全重启，所有状态会重置
- 建议在重要操作前避免长时间不操作设备
- 下电模式下无法通过其他按键唤醒设备

## 优势

1. **极低功耗**: 下电模式功耗降低200-500倍
2. **自动管理**: 无需手动操作，自动进入和唤醒
3. **可靠唤醒**: 三个独立按键确保唤醒可靠性
4. **完全断电**: USB和RF完全断电，避免待机功耗
5. **快速响应**: 按键唤醒响应时间极短
