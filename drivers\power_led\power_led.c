#include "power_led.h"
#include "CH58x_common.h"
#include "soc.h"
#include "PM/pm_task.h"

// LED控制函数
void power_led_init(void)
{
    // 配置PB12为推挽输出
    GPIOB_ModeCfg(POWER_LED_PIN, GPIO_ModeOut_PP_5mA);
    power_led_off();  // 初始状态为关闭
}

void power_led_on(void)
{
    GPIOB_SetBits(POWER_LED_PIN);
}

void power_led_off(void)
{
    GPIOB_ResetBits(POWER_LED_PIN);
}

// 上电指示：亮0.5秒
void power_led_power_on_indicate(void)
{
    PRINT("Power on LED indicate\n");
    power_led_on();
    DelayMs(LED_POWER_ON_TIME);
    power_led_off();
}

// 唤醒指示：闪烁两次，每次亮0.5秒，间隔0.3秒
void power_led_wakeup_indicate(void)
{
    PRINT("Wakeup LED indicate\n");
    
    for(int i = 0; i < LED_WAKEUP_BLINK_COUNT; i++) {
        power_led_on();
        DelayMs(LED_WAKEUP_ON_TIME);
        power_led_off();
        
        // 最后一次闪烁后不需要延时
        if(i < LED_WAKEUP_BLINK_COUNT - 1) {
            DelayMs(LED_WAKEUP_OFF_TIME);
        }
    }
}

// 睡眠保持RAM管理函数
bool is_wakeup_from_sleep(void)
{
    wakeup_info_t* info = WAKEUP_INFO_PTR;
    
    // 检查魔数和唤醒源
    if(info->magic_number == WAKEUP_MAGIC_NUMBER && 
       info->wakeup_source == WAKEUP_SOURCE_SLEEP) {
        return true;
    }
    
    return false;
}

void set_wakeup_source(uint8_t source)
{
    wakeup_info_t* info = WAKEUP_INFO_PTR;
    
    info->magic_number = WAKEUP_MAGIC_NUMBER;
    info->wakeup_source = source;
    info->boot_count++;
    
    PRINT("Set wakeup source: %d, boot count: %d\n", source, info->boot_count);
}

void clear_wakeup_info(void)
{
    wakeup_info_t* info = WAKEUP_INFO_PTR;
    
    info->magic_number = 0;
    info->wakeup_source = WAKEUP_SOURCE_POWER_ON;
    info->boot_count = 0;
}

void init_wakeup_info(void)
{
    wakeup_info_t* info = WAKEUP_INFO_PTR;
    
    // 如果魔数不正确，说明是首次上电或RAM数据丢失
    if(info->magic_number != WAKEUP_MAGIC_NUMBER) {
        clear_wakeup_info();
        set_wakeup_source(WAKEUP_SOURCE_POWER_ON);
    }
}

// 立即进入下电模式
void enter_powerdown_immediately(void)
{
    PRINT("Enter powerdown immediately\n");
    DelayMs(10);
    
    // 设置唤醒标志，表示下次启动是从睡眠唤醒
    set_wakeup_source(WAKEUP_SOURCE_SLEEP);
    
    // 配置所有GPIO为低功耗状态
    GPIOA_ResetBits(GPIO_Pin_All);
    GPIOB_ResetBits(GPIO_Pin_All);
    DelayMs(10);
    
    // 配置大部分GPIO为下拉输入以降低功耗
    GPIOA_ModeCfg(GPIO_Pin_All, GPIO_ModeIN_PD);
    GPIOB_ModeCfg(GPIO_Pin_All, GPIO_ModeIN_PD);
    
    // 配置E、Q、R三个唤醒按键为上拉输入，并启用中断
    // E键 (PA0)
    GPIOA_ModeCfg(GPIO_Pin_0, GPIO_ModeIN_PU);
    GPIOA_ITModeCfg(GPIO_Pin_0, GPIO_ITMode_FallEdge);  // 下降沿触发
    PFIC_EnableIRQ(GPIO_A_IRQn);
    
    // Q键 (PB8) 和 R键 (PB9)  
    GPIOB_ModeCfg(GPIO_Pin_8 | GPIO_Pin_9, GPIO_ModeIN_PU);
    GPIOB_ITModeCfg(GPIO_Pin_8 | GPIO_Pin_9, GPIO_ITMode_FallEdge);  // 下降沿触发
    PFIC_EnableIRQ(GPIO_B_IRQn);
    
    PRINT("Configured wakeup pins: PA0(E), PB8(Q), PB9(R)\n");
    DelayMs(2);
    
    // 进入深度睡眠模式，保持部分RAM
    // 参数说明：
    // 0: 保持所有RAM
    // RB_PWR_RAM2K: 只保持2KB RAM (0x20007800-0x20007FFF)
    // RB_PWR_RAM4K: 只保持4KB RAM (0x20007000-0x20007FFF)
    // 我们使用RB_PWR_RAM2K来保持包含唤醒信息的RAM区域
    LowPower_Shutdown(RB_PWR_RAM2K);
}
