#ifndef __POWER_LED_H
#define __POWER_LED_H

#include <stdint.h>
#include <stdbool.h>
#include "CH58x_common.h"

#ifdef __cplusplus
extern "C" {
#endif

// LED控制引脚定义 (PB12)
#define POWER_LED_PIN           GPIO_Pin_12
#define POWER_LED_PORT          GPIOB

// 睡眠保持RAM区域定义 (使用高地址RAM区域，在LowPower_Shutdown时保持)
// CH582的RAM保持区域通常在0x20007C00-0x20007FFF (1KB)
#define SLEEP_RETAIN_RAM_BASE   0x20007C00
#define SLEEP_RETAIN_RAM_SIZE   1024

// 唤醒标志结构体
typedef struct {
    uint32_t magic_number;      // 魔数，用于验证数据有效性
    uint8_t wakeup_source;      // 唤醒源标识
    uint8_t boot_count;         // 启动计数
    uint8_t reserved[2];        // 保留字节
} wakeup_info_t;

// 魔数定义
#define WAKEUP_MAGIC_NUMBER     0x12345678

// 唤醒源定义
#define WAKEUP_SOURCE_POWER_ON  0x00    // 上电启动
#define WAKEUP_SOURCE_SLEEP     0x01    // 从睡眠唤醒

// 睡眠保持RAM访问指针
#define WAKEUP_INFO_PTR         ((wakeup_info_t*)SLEEP_RETAIN_RAM_BASE)

// LED指示时间定义 (毫秒)
#define LED_POWER_ON_TIME       500     // 上电指示时间
#define LED_WAKEUP_ON_TIME      500     // 唤醒闪烁亮时间
#define LED_WAKEUP_OFF_TIME     300     // 唤醒闪烁灭时间
#define LED_WAKEUP_BLINK_COUNT  2       // 唤醒闪烁次数

// 函数声明
void power_led_init(void);
void power_led_on(void);
void power_led_off(void);
void power_led_power_on_indicate(void);
void power_led_wakeup_indicate(void);

bool is_wakeup_from_sleep(void);
void set_wakeup_source(uint8_t source);
void clear_wakeup_info(void);
void init_wakeup_info(void);

// 立即进入下电模式
void enter_powerdown_immediately(void);

#ifdef __cplusplus
}
#endif

#endif /* __POWER_LED_H */
