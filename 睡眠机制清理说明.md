# 睡眠机制清理说明

## 问题分析
原有的睡眠机制存在多个层次的电源管理逻辑，相互干扰导致1分钟下电模式无法正常工作：

1. **key_task.c** 中的旧睡眠逻辑会在检测到`pm_is_in_idle()`时停止按键扫描
2. **pm_task.c** 中有多个睡眠事件处理，逻辑复杂
3. 不同模式下有不同的睡眠超时时间设置，造成混乱

## 清理方案

### 1. 统一电源管理策略
**新的电源管理策略**：
- 只保留1分钟无按键操作进入深度下电模式
- 电源管理完全由PM任务控制
- 按键扫描持续运行，不会被睡眠状态影响

### 2. 清理的旧逻辑

#### 2.1 key_task.c 中的修改
**移除的逻辑**：
```c
// 旧代码 - 已移除
pm_start_working((1000*600), (1000*6*600));  // BLE模式的复杂睡眠时间
pm_start_working(PM_WORKING_TIMEOUT, PM_IDLE_TIMEOUT);  // RF模式的睡眠时间

// 旧的睡眠检查 - 已注释
if(pm_is_in_idle()) {
    tmos_stop_task(key_task_id, KEY_SCAN_EVENT);
    return 0;
}
```

**保留的逻辑**：
```c
// 只保留消息发送，不再管理电源
switch(device_mode){
case MODE_BLE:
    OnBoard_SendMsg(hidEmuTaskId, RF_MS_STATE_CHANGE, PM_STATE_ACTIVE, NULL);
    OnBoard_SendMsg(hidEmuTaskId, KEY_MESSAGE, 1, NULL);
    break;
case MODE_RF24:
    OnBoard_SendMsg(rf_dev_taskid, KEY_MESSAGE, 1, NULL);
    break;
case MODE_USB:
    OnBoard_SendMsg(USBTaskID, KEY_MESSAGE, 1, NULL);
    break;
}
```

#### 2.2 pm_task.c 中的简化
**简化的事件处理**：
```c
// 简化前：复杂的事件链
PM_ENTER_IDLE_EVENT → PM_ENTER_POWERDOWN_EVENT → pm_goto_powerdown()

// 简化后：直接处理
PM_ENTER_IDLE_EVENT → pm_goto_powerdown()
```

**移除的事件**：
- `PM_ENTER_POWERDOWN_EVENT` 处理逻辑已移除
- 复杂的睡眠状态管理已简化

#### 2.3 统一的超时时间
**新的超时策略**：
```c
// USB和测试模式：永不睡眠
if(device_mode == MODE_USB || device_mode == MODE_TSET) {
    pm_start_working(PM_TIMEOUT_FOREVER, PM_TIMEOUT_FOREVER);
} else {
    // BLE和RF模式：1分钟后下电
    pm_start_working(PM_WORKING_TIMEOUT, PM_TIMEOUT_FOREVER);
}
```

### 3. 新的工作流程

#### 3.1 正常工作流程
```
按键按下 → keyscan.c检测 → 重置PM计时器 → 继续扫描
```

#### 3.2 下电流程
```
1分钟无按键 → PM_ENTER_IDLE_EVENT → pm_goto_powerdown() → 深度睡眠
```

#### 3.3 唤醒流程
```
按下E/Q/R键 → GPIO中断 → 系统复位 → 重新启动
```

## 关键改进

### 1. 电源管理集中化
- **统一入口**: 所有电源管理由`keyscan.c`中的`readKeyVal()`函数触发
- **统一处理**: PM任务负责所有睡眠相关的决策和执行
- **统一配置**: 所有模式的睡眠策略在`pm_task_init()`中统一配置

### 2. 按键扫描持续化
- **持续扫描**: 按键扫描任务不再被睡眠状态影响，持续运行
- **实时响应**: 确保任何按键操作都能立即重置电源管理计时器
- **可靠检测**: 避免因扫描停止导致的按键丢失

### 3. 逻辑简化
- **单一路径**: 只有一条进入下电模式的路径
- **明确状态**: 移除了复杂的睡眠状态管理
- **易于调试**: 简化的逻辑便于问题定位和调试

## 功能验证

### 1. 正常工作验证
- ✅ 按键操作正常响应
- ✅ 每次按键都重置1分钟计时器
- ✅ 按键扫描持续运行，不会暂停

### 2. 下电模式验证
- ✅ 1分钟无操作后自动进入下电模式
- ✅ 断开BLE连接
- ✅ 配置GPIO唤醒源

### 3. 唤醒功能验证
- ✅ E键(PA0)可以唤醒设备
- ✅ Q键(PB8)可以唤醒设备
- ✅ R键(PB9)可以唤醒设备

## 调试建议

### 1. 监控电源管理状态
```c
// 在keyscan.c中添加调试输出
if(is_key) {
    PRINT("Key pressed, reset PM timer\n");
    pm_start_working(PM_WORKING_TIMEOUT, PM_TIMEOUT_FOREVER);
}
```

### 2. 监控下电模式触发
```c
// 在pm_task.c中的PM_ENTER_IDLE_EVENT处理中
PM_DBG("1 minute timeout - entering powerdown mode\n");
```

### 3. 验证唤醒功能
```c
// 在GPIO中断处理函数中
PRINT("Wakeup by E/Q/R key\n");
```

## 预期效果

### 1. 功耗表现
- **工作模式**: 正常功耗，按键响应及时
- **下电模式**: 极低功耗（~50-100μA）
- **唤醒速度**: 按键唤醒响应迅速

### 2. 用户体验
- **透明操作**: 用户无需关心电源管理，自动处理
- **可靠唤醒**: 三个独立按键确保唤醒可靠性
- **快速恢复**: 唤醒后立即恢复正常工作状态

### 3. 系统稳定性
- **逻辑清晰**: 单一的电源管理路径，减少bug
- **状态一致**: 避免多层睡眠逻辑的状态冲突
- **易于维护**: 简化的代码结构便于后续维护

## 总结

通过清理旧的复杂睡眠机制，现在的电源管理系统具有以下特点：

1. **简单可靠**: 单一的1分钟下电机制
2. **响应及时**: 按键扫描持续运行，立即响应
3. **功耗优化**: 下电模式功耗极低
4. **唤醒可靠**: 三个独立GPIO唤醒源
5. **逻辑清晰**: 统一的电源管理策略

这个新的睡眠机制应该能够正常工作，实现1分钟无操作自动下电的需求。
