# 按键映射修改说明

## 修改概述
根据用户需求，对键盘的按键映射进行了重新配置，只保留指定的按键功能，其余按键设置为无效。

## GPIO引脚与按键索引对应关系

### IOmap数组映射 (列引脚)
- IOmap[0] = PB4 (列0)
- IOmap[1] = PB5 (列1) 
- IOmap[2] = PB6 (列2)
- IOmap[3] = PB7 (列3)
- IOmap[4] = PB14 (列4)
- IOmap[5] = PB15 (列5)
- IOmap[6] = PB16 (列6)
- IOmap[7] = PB17 (列7)
- IOmap[8] = PB8 (列8)
- IOmap[9] = PB9 (列9)
- IOmap[10] = PA8 (列10) - 特殊处理
- IOmap[11] = PB18 (列11)
- IOmap[12] = PB19 (列12)
- IOmap[13] = PB20 (列13)
- IOmap[14] = PB21 (列14)
- IOmap[15] = PB22 (列15)
- IOmap[16] = PB23 (列16)

### 行引脚映射
- Key_S0 = PA0 (行1)
- Key_S1 = PA1 (行2) 
- Key_S2 = PA2 (行3)
- Key_S3 = PA3 (行4)
- Key_S4 = PA4 (行5)
- Key_S5 = PA5 (行6)

### 按键索引计算公式
索引 = 列号 × 7 + 行号

## 用户指定的按键映射

| GPIO引脚组合 | 按键功能 | 列号 | 行号 | 计算索引 | HID键值 |
|-------------|---------|------|------|----------|---------|
| (PA1,PB5)   | '1'键   | 1    | 2    | 9        | 0x1E    |
| (PA1,PB6)   | '2'键   | 2    | 2    | 16       | 0x1F    |
| (PA1,PB7)   | '3'键   | 3    | 2    | 23       | 0x20    |
| (PA1,PB14)  | '4'键   | 4    | 2    | 30       | 0x21    |
| (PA1,PB15)  | '5'键   | 5    | 2    | 37       | 0x22    |
| (PA1,PB16)  | '6'键   | 6    | 2    | 44       | 0x23    |
| (PA1,PB19)  | '7'键   | 12   | 2    | 86       | 0x24    |
| (PA4,PB5)   | 'A'键   | 1    | 5    | 12       | 0x04    |
| (PA2,PB19)  | 'B'键   | 12   | 3    | 87       | 0x05    |
| (PA5,PB21)  | 'C'键   | 14   | 6    | 104      | 0x06    |
| (PA5,PB6)   | 'D'键   | 2    | 6    | 20       | 0x07    |
| (PA1,PB18)  | 'F'键   | 11   | 2    | 79       | 0x09    |
| (PA4,PA8)   | 'G'键   | 10   | 5    | 75       | 0x0A    |
| (PA4,PB17)  | 'M'键   | 7    | 5    | 54       | 0x10    |
| (PA4,PB6)   | 'S'键   | 2    | 5    | 19       | 0x16    |
| (PA2,PB18)  | 'T'键   | 11   | 3    | 80       | 0x17    |
| (PA2,PB15)  | 'V'键   | 5    | 3    | 38       | 0x19    |
| (PA2,PB6)   | 'W'键   | 2    | 3    | 17       | 0x1A    |
| (PA2,PB4)   | 'X'键   | 0    | 3    | 3        | 0x1B    |
| (PA2,PB22)  | 'Z'键   | 15   | 3    | 108      | 0x1D    |
| (PA5,PB7)   | 空格键   | 3    | 6    | 27       | 0x2C    |
| (PA5,PB4)   | CTRL键  | 0    | 6    | 6        | 0x01    |
| (PA5,PB17)  | SHIFT键 | 7    | 6    | 55       | 0x02    |

## 修改的文件

### 1. drivers/key_scan/key_table.h
- 重新定义了 `key8_table[]` 数组
- 将所有不需要的按键位置设置为 0x00 (无按键)
- 只在指定的索引位置设置对应的HID键值

### 2. drivers/key_scan/key_parse.c
- 修改了 `hotkeydeal()` 函数
- 只保留SHIFT键(索引55)和CTRL键(索引6)的处理
- 移除了其他修饰键的处理逻辑

## HID键值说明
- 数字键1-7: 0x1E-0x24
- 字母键A-Z: 0x04-0x1D (按字母顺序)
- 空格键: 0x2C
- CTRL键: 0x01 (修饰键位)
- SHIFT键: 0x02 (修饰键位)

## 注意事项
1. 修饰键(CTRL、SHIFT)会被特殊处理，放在HID报告的第一个字节中
2. 普通按键会被放在HID报告的第3-8字节中
3. 所有未指定的按键位置都设置为0x00，表示无按键功能
4. 按键扫描的硬件连接和扫描逻辑保持不变，只修改了软件映射表
