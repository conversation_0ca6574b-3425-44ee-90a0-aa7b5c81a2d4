/*
 * Copyright (c) 2022 zerosensei
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#ifndef SOC_SOC_H
#define SOC_SOC_H

#include <toolchain/gcc.h>
#include "CH58x_common.h"
#include <debug/DEBUG.h>
#include "HAL/HAL.h"

//#define DEFINE_DEFAULT_IRQ_HANDLER(irq_handler) void irq_handler() __attribute__((interrupt()))
//
//DEFINE_DEFAULT_IRQ_HANDLER(NMI_Handler);
//DEFINE_DEFAULT_IRQ_HANDLER(HardFault_Handler);
//DEFINE_DEFAULT_IRQ_HANDLER(Ecall_U_Mode_Handler);
//DEFINE_DEFAULT_IRQ_HANDLER(SysTick_Handler);
//DEFINE_DEFAULT_IRQ_HANDLER(SW_Handler);
//DEFINE_DEFAULT_IRQ_HANDLER(TMR0_IRQHandler);
//DEFINE_DEFAULT_IRQ_HANDLER(GPIOA_IRQHandler);
//DEFINE_DEFAULT_IRQ_HANDLER(GPIOB_IRQHandler);
//DEFINE_DEFAULT_IRQ_HANDLER(SPI0_IRQHandler);
//DEFINE_DEFAULT_IRQ_HANDLER(BB_IRQHandler);
//DEFINE_DEFAULT_IRQ_HANDLER(LLE_IRQHandler);
//DEFINE_DEFAULT_IRQ_HANDLER(USB_IRQHandler);
//DEFINE_DEFAULT_IRQ_HANDLER(TMR1_IRQHandler);
//DEFINE_DEFAULT_IRQ_HANDLER(TMR2_IRQHandler);
//DEFINE_DEFAULT_IRQ_HANDLER(UART0_IRQHandler);
//DEFINE_DEFAULT_IRQ_HANDLER(UART1_IRQHandler);
//DEFINE_DEFAULT_IRQ_HANDLER(RTC_IRQHandler);
//DEFINE_DEFAULT_IRQ_HANDLER(ADC_IRQHandler);
//DEFINE_DEFAULT_IRQ_HANDLER(PWMX_IRQHandler);
//DEFINE_DEFAULT_IRQ_HANDLER(TMR3_IRQHandler);
//DEFINE_DEFAULT_IRQ_HANDLER(UART2_IRQHandler);
//DEFINE_DEFAULT_IRQ_HANDLER(UART3_IRQHandler);
//DEFINE_DEFAULT_IRQ_HANDLER(I2C_IRQHandler);
//DEFINE_DEFAULT_IRQ_HANDLER(WDOG_BAT_IRQHandler);

#define DEFINE_FAST_IRQ_HANDLER(irq_handler) void irq_handler() __attribute__((interrupt("WCH-Interrupt-fast")))

DEFINE_FAST_IRQ_HANDLER(NMI_Handler);
DEFINE_FAST_IRQ_HANDLER(HardFault_Handler);
DEFINE_FAST_IRQ_HANDLER(Ecall_U_Mode_Handler);
DEFINE_FAST_IRQ_HANDLER(SysTick_Handler);
DEFINE_FAST_IRQ_HANDLER(SW_Handler);
DEFINE_FAST_IRQ_HANDLER(TMR0_IRQHandler);
DEFINE_FAST_IRQ_HANDLER(GPIOA_IRQHandler);
DEFINE_FAST_IRQ_HANDLER(GPIOB_IRQHandler);
DEFINE_FAST_IRQ_HANDLER(SPI0_IRQHandler);
DEFINE_FAST_IRQ_HANDLER(BB_IRQHandler);
DEFINE_FAST_IRQ_HANDLER(LLE_IRQHandler);
DEFINE_FAST_IRQ_HANDLER(USB_IRQHandler);
DEFINE_FAST_IRQ_HANDLER(TMR1_IRQHandler);
DEFINE_FAST_IRQ_HANDLER(TMR2_IRQHandler);
DEFINE_FAST_IRQ_HANDLER(UART0_IRQHandler);
DEFINE_FAST_IRQ_HANDLER(UART1_IRQHandler);
DEFINE_FAST_IRQ_HANDLER(RTC_IRQHandler);
DEFINE_FAST_IRQ_HANDLER(ADC_IRQHandler);
DEFINE_FAST_IRQ_HANDLER(PWMX_IRQHandler);
DEFINE_FAST_IRQ_HANDLER(TMR3_IRQHandler);
DEFINE_FAST_IRQ_HANDLER(UART2_IRQHandler);
DEFINE_FAST_IRQ_HANDLER(UART3_IRQHandler);
DEFINE_FAST_IRQ_HANDLER(I2C_IRQHandler);
DEFINE_FAST_IRQ_HANDLER(WDOG_BAT_IRQHandler);


static inline uint32_t get_current_time(void)
{
    return RTC_TO_MS(RTC_GetCycle32k());
}

#define WAIT_FOR_DBG    \
while((R8_UART1_LSR & RB_LSR_TX_ALL_EMP) == 0)  \
{   \
    __nop();    \
}   \

void SystemInit(void);


#endif /* SOC_SOC_H */
