# 睡眠模式LED指示和日志增强说明

## 功能概述
在保持现有功能不变的前提下，为睡眠模式添加了详细的LED指示和关键日志输出，让用户能够清楚地了解设备的状态。

## LED指示方案

### 1. 上电启动指示
- **行为**: LED亮0.5秒，然后熄灭
- **时机**: 检测到上电复位后，进入睡眠模式前
- **含义**: 设备确认上电，即将进入睡眠等待唤醒
- **实现**: `power_led_power_on_indicate()`函数

### 2. 唤醒指示
- **行为**: LED快速闪烁两次（每次200ms亮，200ms灭）
- **时机**: 检测到从睡眠唤醒后，开始正常工作前
- **含义**: 设备确认从睡眠唤醒，即将开始正常工作
- **实现**: `power_led_wakeup_indicate()`函数

### 3. 兼容性
- **保留**: 原有的`power_led_blink_once()`函数保持不变
- **扩展**: 新增专用的指示函数，不影响现有代码

## 关键日志增强

### 1. 启动阶段日志
```
=== SYSTEM STARTUP ===
Reading reset status register...
Reset flag: 0x01
R8_RESET_STATUS register: 0x01
```

### 2. 上电启动日志
```
=== POWER ON RESET DETECTED ===
This is a fresh power-on, will enter sleep mode after LED indication
LED: Power on indication (0.5s)
Configuring wakeup keys...
Wakeup keys configured: PA0(E), PB8(Q), PB9(R)

=== ENTERING SHUTDOWN MODE ===
Preparing to enter shutdown mode...
LED turned off
*** SYSTEM ENTERING SLEEP MODE ***
*** Press E/Q/R keys to wake up ***
```

### 3. 唤醒启动日志
```
=== SHUTDOWN WAKEUP RESET DETECTED ===
Device was woken up from sleep mode by key press
LED: Wakeup indication (blink twice)
=== RESUMING NORMAL OPERATION ===
[继续正常系统初始化...]
```

### 4. 其他复位日志
```
=== OTHER RESET DETECTED ===
Reset flag: 0x02 (Watchdog/Software/Other reset)
Entering normal operation mode
=== CONTINUING NORMAL OPERATION ===
```

## 实现细节

### 1. 新增函数

#### LED指示函数
```c
// 上电指示：亮0.5秒
void power_led_power_on_indicate(void);

// 唤醒指示：闪烁两次
void power_led_wakeup_indicate(void);
```

#### 实现特点
- **非阻塞**: 使用DelayMs()确保指示完整显示
- **有日志**: 每个指示都有对应的日志输出
- **时序准确**: 严格按照需求的时间执行

### 2. 日志增强位置

#### 进入睡眠前
- 明确标识即将进入睡眠模式
- 提示用户按键唤醒方法
- 确认所有配置完成

#### 唤醒后
- 确认唤醒原因和类型
- 显示LED指示状态
- 标识即将开始正常工作

### 3. 保持兼容性

#### 现有功能不变
- 所有原有函数保持不变
- 原有的调用方式继续有效
- 不影响现有的业务逻辑

#### 扩展性设计
- 新功能作为独立函数添加
- 可以选择性使用新的指示功能
- 便于后续功能扩展

## 用户体验

### 1. 上电体验
```
用户操作: 给设备上电
设备反应: LED亮0.5秒 → LED熄灭 → 设备进入睡眠
用户感知: 设备已启动并进入省电模式，等待按键唤醒
```

### 2. 唤醒体验
```
用户操作: 按下E/Q/R任意键
设备反应: LED快速闪烁两次 → 设备开始正常工作
用户感知: 设备已唤醒并开始工作
```

### 3. 调试体验
```
开发者: 通过串口可以看到详细的状态日志
内容: 复位原因、LED状态、配置过程、睡眠/唤醒确认
价值: 便于问题排查和功能验证
```

## 调试控制

### 调试模式开关
```c
#define SKIP_SLEEP_FOR_DEBUG 0  // 0=启用睡眠, 1=跳过睡眠用于调试
```

### 调试模式功能
- **SKIP_SLEEP_FOR_DEBUG = 1**: 跳过睡眠逻辑，用于基本功能测试
- **SKIP_SLEEP_FOR_DEBUG = 0**: 启用完整睡眠功能，正常工作模式

## 测试验证

### 1. 上电测试
- **预期**: LED亮0.5秒后熄灭，串口显示上电日志
- **验证**: 观察LED行为和串口输出是否符合预期

### 2. 唤醒测试
- **预期**: 按键后LED闪烁两次，串口显示唤醒日志
- **验证**: 分别测试E/Q/R三个按键的唤醒效果

### 3. 功能测试
- **预期**: 唤醒后设备正常工作，所有功能正常
- **验证**: 确认键盘、蓝牙等功能不受影响

### 4. 循环测试
- **预期**: 多次睡眠-唤醒循环稳定工作
- **验证**: 长时间测试确认功能稳定性

## 注意事项

### 1. 时序要求
- LED指示时间严格按照需求执行
- 确保用户能够清楚看到指示效果

### 2. 功耗考虑
- LED指示时间尽量短，减少功耗影响
- 睡眠模式下LED完全关闭

### 3. 兼容性
- 不修改现有函数的行为
- 新功能作为可选增强，不影响原有逻辑

### 4. 调试友好
- 详细的日志输出便于问题排查
- 调试模式开关便于开发测试
