# 编译错误修复说明

## 遇到的编译错误

### 1. 函数名错误
```
warning: implicit declaration of function 'GAPRole_TerminateConnection'
undefined reference to `GAPRole_TerminateConnection'
```

### 2. 重复定义错误
```
multiple definition of `GPIOA_IRQHandler'
```

### 3. 缺少头文件包含
```
warning: implicit declaration of function 'RTC_SetTignTime'
```

## 修复方案

### 1. 修复BLE断开连接函数名
**问题**: `GAPRole_TerminateConnection()` 函数不存在
**解决**: 使用正确的函数 `GAPRole_TerminateLink()`

**修改文件**: `subsys/PM/pm_task.c`
```c
// 修改前
GAPRole_TerminateConnection();

// 修改后  
GAPRole_TerminateLink(GAP_CONNHANDLE_ALL);
```

**说明**: 
- `GAPRole_TerminateLink()` 是正确的BLE连接断开函数
- `GAP_CONNHANDLE_ALL` 参数表示断开所有连接
- 该函数在 `CH58xBLE_LIB.h` 中定义

### 2. 解决GPIO中断处理函数重复定义
**问题**: `GPIOA_IRQHandler` 在多个文件中定义
**解决**: 删除重复定义，统一使用现有的中断处理函数

**修改文件**: 
- `soc/soc.c`: 删除重复的 `GPIOA_IRQHandler` 定义
- `subsys/key_task/key_task.c`: 扩展现有的 `GPIOA_IRQHandler` 功能

**修改后的key_task.c中的GPIOA_IRQHandler:**
```c
__HIGH_CODE
void GPIOA_IRQHandler(void){
    no_key_count = 0;
    DelayUs(150);
    
    // 处理E键(PA0)唤醒
    if(GPIOA_ReadITFlagBit(GPIO_Pin_0)) {
        GPIOA_ClearITFlagBit(GPIO_Pin_0);
        PRINT("Wakeup by E key (PA0)\n");
    }
    
    // 处理原有的GPIO_Pin_2中断
    if(GPIOA_ReadITFlagBit(GPIO_Pin_2)) {
        GPIOA_ClearITFlagBit(GPIO_Pin_2);
    }
    
    // 清除所有可能的中断标志位
    GPIOA_ClearITFlagBit(GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_2 | GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_6);
}
```

### 3. 添加必要的头文件包含
**问题**: 缺少BLE相关函数的声明
**解决**: 在pm_task.c中添加BLE库头文件

**修改文件**: `subsys/PM/pm_task.c`
```c
#include "CH58xBLE_LIB.h"  // 添加BLE库头文件
```

### 4. 保持GPIOB中断处理函数
**说明**: `GPIOB_IRQHandler` 没有重复定义问题，保持在soc.c中的实现

**soc.c中的GPIOB_IRQHandler:**
```c
__HIGH_CODE
__INTERRUPT
void GPIOB_IRQHandler(void)
{
    if(GPIOB_ReadITFlagBit(GPIO_Pin_8)) {
        GPIOB_ClearITFlagBit(GPIO_Pin_8);
        PRINT("Wakeup by Q key (PB8)\n");
    }
    
    if(GPIOB_ReadITFlagBit(GPIO_Pin_9)) {
        GPIOB_ClearITFlagBit(GPIO_Pin_9);
        PRINT("Wakeup by R key (PB9)\n");
    }
    
    // 清除所有可能的中断标志位
    GPIOB_ClearITFlagBit(GPIO_Pin_8 | GPIO_Pin_9);
}
```

## 修复后的功能验证

### 1. BLE连接断开
- 使用正确的 `GAPRole_TerminateLink(GAP_CONNHANDLE_ALL)` 函数
- 能够正确断开所有BLE连接
- 进入下电模式前确保连接已断开

### 2. GPIO唤醒中断
- **PA0 (E键)**: 由key_task.c中的GPIOA_IRQHandler处理
- **PB8 (Q键)**: 由soc.c中的GPIOB_IRQHandler处理  
- **PB9 (R键)**: 由soc.c中的GPIOB_IRQHandler处理

### 3. 中断标志位清除
- 正确清除对应的GPIO中断标志位
- 避免中断重复触发
- 提供调试输出信息

## 编译结果
修复后应该能够正常编译通过，没有以下错误：
- ✅ 解决了函数未定义错误
- ✅ 解决了重复定义错误  
- ✅ 添加了必要的头文件包含
- ✅ 保持了原有功能的完整性

## 功能完整性
修复后的代码保持了以下功能：
1. **下电省电模式**: 1分钟无按键后自动进入
2. **BLE连接管理**: 正确断开BLE连接
3. **GPIO唤醒**: E、Q、R三个按键可以唤醒设备
4. **中断处理**: 正确的GPIO中断处理和标志位清除
5. **调试支持**: 保留了调试输出信息

## 注意事项
1. **中断优先级**: 确保GPIO中断优先级设置正确
2. **唤醒时序**: 唤醒后系统会完全复位重启
3. **功耗测试**: 建议实际测试下电模式的功耗表现
4. **连接稳定性**: 测试BLE连接断开和重连的稳定性
