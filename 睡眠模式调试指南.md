# 睡眠模式调试指南

## 问题分析

### 原始问题
设备上电后没有任何反应，没有日志输出，按键也无响应。

### 根本原因
**串口初始化时机错误**：在检查复位标志并决定进入睡眠之前，串口还没有初始化，所以看不到任何调试输出。

## 修复措施

### 1. 调整初始化顺序
```c
int main(void)
{
    // 基础系统初始化
    PowerMonitor(ENABLE, LPLevel_2V5);
    PFIC_EnableIRQ(WDOG_BAT_IRQn);
    
    // *** 关键修复：在检查复位标志前初始化串口 ***
#ifdef DEBUG
    GPIOA_SetBits(bTXD1);
    GPIOA_ModeCfg(bTXD1, GPIO_ModeOut_PP_5mA);
    UART1_DefInit();
    UART1_BaudRateCfg(115200);
#endif
    DEBUG_Init();
    
    // 现在可以安全地输出调试信息了
    PRINT("=== SYSTEM STARTUP ===\n");
    
    // 读取复位标志并决定启动模式
    uint8_t reset_flag = get_reset_flag();
    // ...
}
```

### 2. 添加调试模式
为了调试，临时添加了跳过睡眠逻辑的选项：
```c
#define SKIP_SLEEP_FOR_DEBUG 1

#if SKIP_SLEEP_FOR_DEBUG
    PRINT("=== DEBUG MODE - SKIPPING SLEEP LOGIC ===\n");
    power_led_init();
    power_led_blink_once();  // 测试LED
    // 继续正常启动，不进入睡眠
#else
    // 正常的睡眠逻辑
#endif
```

## 调试步骤

### 第一步：验证基本功能
1. **编译并烧录**修复后的代码
2. **观察串口输出**，应该看到：
   ```
   === SYSTEM STARTUP ===
   Reading reset status register...
   Reset flag: 0xXX
   R8_RESET_STATUS register: 0xXX
   === DEBUG MODE - SKIPPING SLEEP LOGIC ===
   LED test completed, continuing with normal startup...
   ```
3. **观察LED**：应该看到PB12的LED闪烁一次
4. **确认系统正常启动**：后续的初始化日志应该正常输出

### 第二步：分析复位标志
根据串口输出的复位标志值：
- **0x01**: 上电复位 - 正常，首次上电应该是这个值
- **0x05**: 唤醒复位 - 说明是从睡眠唤醒
- **其他值**: 异常复位 - 需要进一步分析

### 第三步：测试睡眠功能
确认基本功能正常后，修改代码启用睡眠逻辑：
```c
#define SKIP_SLEEP_FOR_DEBUG 0  // 改为0启用睡眠逻辑
```

然后测试：
1. **上电测试**：设备应该输出日志后进入睡眠
2. **唤醒测试**：按E/Q/R键应该能唤醒设备
3. **循环测试**：唤醒后应该正常工作，不会再次进入睡眠

## 可能的其他问题

### 1. 复位标志读取错误
如果复位标志读取不正确，检查：
- `R8_RESET_STATUS`寄存器是否存在
- 寄存器地址是否正确
- 位掩码是否正确

### 2. GPIO配置问题
如果LED不闪烁，检查：
- PB12引脚是否正确
- GPIO时钟是否使能
- 引脚配置是否正确

### 3. 睡眠模式问题
如果无法进入睡眠或无法唤醒，检查：
- `LowPower_Shutdown()`函数是否存在
- 唤醒源配置是否正确
- GPIO中断处理是否正常

## 调试输出示例

### 正常上电（调试模式）
```
=== SYSTEM STARTUP ===
Reading reset status register...
Reset flag: 0x01
R8_RESET_STATUS register: 0x01
=== DEBUG MODE - SKIPPING SLEEP LOGIC ===
LED test completed, continuing with normal startup...
[后续正常初始化日志...]
```

### 正常上电（睡眠模式）
```
=== SYSTEM STARTUP ===
Reading reset status register...
Reset flag: 0x01
R8_RESET_STATUS register: 0x01
=== POWER ON RESET - ENTERING SLEEP MODE ===
Configuring wakeup keys...
Wakeup keys configured: PA0(E), PB8(Q), PB9(R)
Preparing to enter shutdown mode...
Entering shutdown mode now...
[日志停止，设备进入睡眠]
```

### 按键唤醒
```
=== SYSTEM STARTUP ===
Reading reset status register...
Reset flag: 0x05
R8_RESET_STATUS register: 0x05
=== SHUTDOWN WAKEUP RESET - NORMAL OPERATION ===
[LED闪烁一次]
[后续正常初始化日志...]
```

## 下一步计划

1. **验证修复**：确认设备能正常启动并输出日志
2. **测试LED**：确认LED控制功能正常
3. **分析复位标志**：确认复位检测逻辑正确
4. **启用睡眠**：逐步启用完整的睡眠功能
5. **完整测试**：测试完整的睡眠-唤醒循环
