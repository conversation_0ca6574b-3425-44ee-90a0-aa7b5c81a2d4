{"folders": [{"path": "."}], "settings": {"files.autoGuessEncoding": true, "C_Cpp.default.configurationProvider": "cl.eide", "files.associations": {".eideignore": "ignore", "*.h": "c", "*.c": "c", "*.hxx": "cpp", "*.hpp": "cpp", "*.c++": "cpp", "*.cpp": "cpp", "*.cxx": "cpp", "*.cc": "cpp", "cstdio": "c", "cstdint": "c"}, "[yaml]": {"editor.insertSpaces": true, "editor.tabSize": 4, "editor.autoIndent": "advanced"}, "EIDE.RISCV.InstallDirectory": "d:\\MounRiver\\MounRiver_Studio\\toolchain\\RISC-V Embedded GCC", "files.encoding": "gbk"}, "extensions": {"recommendations": ["cl.eide", "keroc.hex-fmt", "xiaoyongdong.srecord", "hars.cppsnippets", "zixuanwang.linkerscript", "redhat.vscode-yaml"]}, "launch": {"configurations": [], "compounds": []}, "tasks": {"version": "2.0.0", "tasks": []}}