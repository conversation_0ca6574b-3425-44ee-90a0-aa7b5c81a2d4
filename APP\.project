<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<projectDescription>
	<name>Vanm_Keyboard</name>
	<comment/>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<triggers>clean,full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>LIB</name>
			<type>2</type>
			<locationURI>PARENT-1-PROJECT_LOC/LIB</locationURI>
		</link>
		<link>
			<name>Startup</name>
			<type>2</type>
			<locationURI>PARENT-1-PROJECT_LOC/Startup</locationURI>
		</link>
		<link>
			<name>StdPeriphDriver</name>
			<type>2</type>
			<locationURI>PARENT-1-PROJECT_LOC/StdPeriphDriver</locationURI>
		</link>
		<link>
			<name>boards</name>
			<type>2</type>
			<locationURI>PARENT-1-PROJECT_LOC/boards</locationURI>
		</link>
		<link>
			<name>doc</name>
			<type>2</type>
			<locationURI>PARENT-1-PROJECT_LOC/doc</locationURI>
		</link>
		<link>
			<name>drivers</name>
			<type>2</type>
			<locationURI>PARENT-1-PROJECT_LOC/drivers</locationURI>
		</link>
		<link>
			<name>include</name>
			<type>2</type>
			<locationURI>PARENT-1-PROJECT_LOC/include</locationURI>
		</link>
		<link>
			<name>kernel</name>
			<type>2</type>
			<locationURI>PARENT-1-PROJECT_LOC/kernel</locationURI>
		</link>
		<link>
			<name>soc</name>
			<type>2</type>
			<locationURI>PARENT-1-PROJECT_LOC/soc</locationURI>
		</link>
		<link>
			<name>subsys</name>
			<type>2</type>
			<locationURI>PARENT-1-PROJECT_LOC/subsys</locationURI>
		</link>
		<link>
			<name>test</name>
			<type>2</type>
			<locationURI>PARENT-1-PROJECT_LOC/test</locationURI>
		</link>
	</linkedResources>
	<filteredResources>
		<filter>
			<id>1634107254788</id>
			<name/>
			<type>22</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-false-false-*.wvproj</arguments>
			</matcher>
		</filter>
	</filteredResources>
</projectDescription>
